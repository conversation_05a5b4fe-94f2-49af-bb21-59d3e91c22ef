plugins {
    id 'com.android.library'
}

android {
    namespace "com.frontier.ironsourcesdk"
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion PROP_TARGET_SDK_VERSION

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

repositories {
    mavenCentral()
    maven {url 'https://cboost.jfrog.io/artifactory/chartboost-ads/' }
    maven { url 'https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea' }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.4.0'
    implementation 'com.google.android.material:material:1.4.0'

    implementation project(':sdkbase')

    implementation 'com.google.android.gms:play-services-appset:16.0.2'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.1.0'
    implementation 'com.google.android.gms:play-services-basement:18.1.0'

    implementation 'com.unity3d.ads-mediation:mediation-sdk:8.10.0'

    // Add Applovin Network
    implementation 'com.unity3d.ads-mediation:applovin-adapter:4.3.54'
    implementation 'com.applovin:applovin-sdk:13.3.1'

    // Add Chartboost Network
    implementation 'com.unity3d.ads-mediation:chartboost-adapter:4.3.22'
    implementation 'com.chartboost:chartboost-sdk:9.9.2'

    // Add Fyber Network (Adapter only) Digital Turbine  DT Exchange
    implementation 'com.unity3d.ads-mediation:fyber-adapter:4.3.40'
    implementation "com.fyber:marketplace-sdk:8.3.8"
    // Add Facebook Network
    implementation 'com.unity3d.ads-mediation:facebook-adapter:4.3.51'
    implementation 'com.facebook.android:audience-network-sdk:6.20.0'
    // Add AdMob and Ad Manager Network
    implementation 'com.unity3d.ads-mediation:admob-adapter:4.3.53'
    implementation 'com.google.android.gms:play-services-ads:24.5.0'
    // Add HyprMX Network
    implementation 'com.unity3d.ads-mediation:hyprmx-adapter:4.3.15'
    implementation 'com.hyprmx.android:HyprMX-SDK:6.4.3'
    // Add InMobi Network
    implementation 'com.unity3d.ads-mediation:inmobi-adapter:4.3.32'
    implementation 'com.inmobi.monetization:inmobi-ads-kotlin:10.8.7'

    // Mintegral Network
    implementation 'com.unity3d.ads-mediation:mintegral-adapter:4.3.45'
    implementation 'com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.91'

    // Add Moloco Network
    implementation 'com.unity3d.ads-mediation:moloco-adapter:4.3.20'
    implementation 'com.moloco.sdk:moloco-sdk:3.12.0'
    // Add UnityAds Network
    implementation 'com.unity3d.ads-mediation:unityads-adapter:4.3.59'
    implementation 'com.unity3d.ads:unity-ads:4.16.1'

    // Add Vungle Network Liftoff Monetize
    implementation 'com.unity3d.ads-mediation:vungle-adapter:4.3.31'
    implementation 'com.vungle:vungle-ads:7.5.1'
}