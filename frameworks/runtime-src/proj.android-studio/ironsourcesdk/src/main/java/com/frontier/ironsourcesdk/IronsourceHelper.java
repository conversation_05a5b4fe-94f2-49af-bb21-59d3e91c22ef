package com.frontier.ironsourcesdk;

import android.app.Activity;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.frontier.sdkbase.AbstractAdImpl;
import com.frontier.sdkbase.AdEvent;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKManager;
import com.frontier.sdkbase.SDKType;
//import com.ironsource.mediationsdk.IronSource;
import com.unity3d.mediation.LevelPlay;
import com.unity3d.mediation.LevelPlayAdError;
import com.unity3d.mediation.LevelPlayAdInfo;
import com.unity3d.mediation.LevelPlayConfiguration;
import com.unity3d.mediation.LevelPlayInitError;
import com.unity3d.mediation.LevelPlayInitListener;
import com.unity3d.mediation.LevelPlayInitRequest;
import com.unity3d.mediation.impression.LevelPlayImpressionData;
import com.unity3d.mediation.impression.LevelPlayImpressionDataListener;
import com.unity3d.mediation.interstitial.LevelPlayInterstitialAd;
import com.unity3d.mediation.interstitial.LevelPlayInterstitialAdListener;
import com.unity3d.mediation.rewarded.LevelPlayReward;
import com.unity3d.mediation.rewarded.LevelPlayRewardedAd;
import com.unity3d.mediation.rewarded.LevelPlayRewardedAdListener;
import com.unity3d.mediation.segment.LevelPlaySegment;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

public class IronsourceHelper extends AbstractAdImpl {

    private static final String TAG = "IronsourceHelper";
    private boolean inited = false;

    private static long showInterstitialTimeSpan = 5000;
    private long showRewardVideoTS = 0;
    private String userID = "unknown";
    LevelPlayRewardedAd mRewardedAd;
    LevelPlayInterstitialAd mInterstitialAd;

    private Runnable addCustomPropertiesTask = null;

    // 提供有关所有广告单元的信息，用来识别广告。获得展示级别的数据
    private LevelPlayImpressionDataListener impressionDataListener = new LevelPlayImpressionDataListener() {
        @Override
        public void onImpressionSuccess(LevelPlayImpressionData impressionData) {
            if(impressionData != null) {
                Double revenue = impressionData.getRevenue();
                String adNetwork = impressionData.getAdNetwork();
                String adFormat = impressionData.getAdFormat();
                String placement = impressionData.getPlacement();
                JSONObject json = new JSONObject();
                try {
                    json.put("revenue", revenue);
                    json.put("adnetwork", adNetwork);
                    json.put("adunit", adFormat);
                    json.put("placement", placement);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                Log.d(TAG, "onImpressionSuccess data=" + json.toString());
                try {
                    Class clazz = Class.forName("org.cocos2dx.bole.BoleJavaUtil");
                    Method method = clazz.getMethod("adjustTrackAdRevenue", String.class);
                    method.invoke(null, json.toString());
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }

                listener.onAdEvent(sdkType(), AdEvent.IMPRESSION_SUCCESS, json, -1);

                JSONObject json2 = new JSONObject();
                try {
                    json2.put("ad_platform", "ironSource");
                    json2.put("ad_source", impressionData.getAdNetwork());
                    json2.put("ad_format", impressionData.getAdFormat());
                    json2.put("ad_unit_name", impressionData.getInstanceName());
                    json2.put("currency", "USD");
                    json2.put("value", impressionData.getRevenue());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                Log.d(TAG, "onImpressionSuccess2 data=" + json2.toString());
                try {
                    Class clazz = Class.forName("org.cocos2dx.bole.BoleJavaUtil");
                    Method method = clazz.getMethod("firebaseLogEvent", String.class, String.class);
                    method.invoke(null, "ad_impression", json2.toString());
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }
            }

        }
    };

    public IronsourceHelper(Activity activity, EventListener listener) {
        super(activity, listener);
        if (IronsourceManager.mIsMultiAdUnitsLoaded && IronsourceManager.mRewardedAd != null && IronsourceManager.mInterstitialAd != null) {
            mRewardedAd = IronsourceManager.mRewardedAd;
            mInterstitialAd = IronsourceManager.mInterstitialAd;

            mRewardedAd.setListener(mLevelPlayRewardedAdListener);
            mInterstitialAd.setListener(mLevelPlayInterstitialAdListener);
        }
    }

    private LevelPlayRewardedAdListener mLevelPlayRewardedAdListener = new LevelPlayRewardedAdListener() {
        @Override
        public void onAdLoaded(LevelPlayAdInfo levelPlayAdInfo) {
            // Ad was loaded successfully
            listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_AD_READY, null, -1);
        }
        @Override
        public void onAdLoadFailed(LevelPlayAdError levelPlayAdError) {
            JSONObject json = new JSONObject();
            try {
                json.put("error_code", levelPlayAdError.getErrorCode());
                json.put("msg", levelPlayAdError.getErrorMessage());
            } catch (JSONException e) {
                e.printStackTrace();
            }
            listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_AD_LOAD_FAILED, json, -1);
        }
        @Override
        public void onAdDisplayed(LevelPlayAdInfo levelPlayAdInfo) {
            // Ad was displayed and visible on screen
            Log.d(TAG, "AdvertiseControl:onAdsCallbackNew log onAdOpened");
            SDKManager.setBuglyUserData("ads", levelPlayAdInfo.getAdNetwork());
            listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_OPENED,null, -1);
        }

        @Override
        public void onAdRewarded(@NonNull LevelPlayReward reward, @NonNull LevelPlayAdInfo adInfo) {
            // Ad reward received
            Log.d(TAG, "AdvertiseControl:onAdsCallbackNew log onAdRewarded");
            JSONObject json = new JSONObject();
            if(reward != null) {
                try {
                    json.put("placement_id", adInfo.getAdId());
                    json.put("placement_name", adInfo.getPlacementName());
                    json.put("reward_name", reward.getName());
                    json.put("reward_amount", reward.getAmount());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_REWARDED, json, -1);
        }
        @Override
        public void onAdDisplayFailed(LevelPlayAdError levelPlayAdError, LevelPlayAdInfo levelPlayAdInfo) {
            // Ad fails to be displayed
            // Optional
            JSONObject json = new JSONObject();
            try {
                json.put("error_code", levelPlayAdError.getErrorCode());
                json.put("msg", levelPlayAdError.getErrorMessage());
            } catch (JSONException e) {
                e.printStackTrace();
            }
            listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_SHOW_FAILED, json, -1);
        }
        @Override
        public void onAdClicked(LevelPlayAdInfo levelPlayAdInfo) {
            // Ad was clicked
            JSONObject json = new JSONObject();
            try {
                json.put("placement_id", levelPlayAdInfo.getAdId());
                json.put("placement_name", levelPlayAdInfo.getPlacementName());
                // json.put("reward_name", levelPlayAdInfo.getRewardName()); // 新的api里已经没有了
                // json.put("reward_amount", levelPlayAdInfo.getRewardAmount()); // 新的api里已经没有了
            } catch (JSONException e) {
                e.printStackTrace();
            }
            listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_AD_CLICKED, json, -1);
        }
        @Override
        public void onAdClosed(LevelPlayAdInfo levelPlayAdInfo) {
            Log.d(TAG, "AdvertiseControl:onAdsCallbackNew log onAdClosed");
            SDKManager.removeBuglyUserData("ads");
            showRewardVideoTS = System.currentTimeMillis();
            listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_CLOSED,null, -1);
        }
        @Override
        public void onAdInfoChanged(LevelPlayAdInfo levelPlayAdInfo) {
            // Called after the ad info is updated. Available when another Rewarded ad has loaded, and includes a higher CPM/Rate
            // Optional
        }
    };

    // Create the interstitial ad object
    private LevelPlayInterstitialAdListener mLevelPlayInterstitialAdListener = new LevelPlayInterstitialAdListener() {
            @Override
            public void onAdLoaded(LevelPlayAdInfo levelPlayAdInfo) {
                // Ad was loaded successfully
                listener.onAdEvent(sdkType(), AdEvent.INTERSTITIAL_AD_READY, null, -1);
            }
            @Override
            public void onAdLoadFailed(LevelPlayAdError levelPlayAdError) {
                // Ad load failed
                JSONObject json = new JSONObject();
                try {
                    json.put("error_code", levelPlayAdError.getErrorCode());
                    json.put("msg", levelPlayAdError.getErrorMessage());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                listener.onAdEvent(sdkType(), AdEvent.INTERSTITIAL_AD_LOAD_FAILED, json, -1);
            }
            @Override
            public void onAdDisplayed(LevelPlayAdInfo levelPlayAdInfo) {
                // Ad was displayed and visible on screen
                SDKManager.setBuglyUserData("ads", levelPlayAdInfo.getAdNetwork());
                listener.onAdEvent(sdkType(), AdEvent.INTERSTITIAL_AD_OPENED, null, -1);
            }
            @Override
            public void onAdDisplayFailed(LevelPlayAdError levelPlayAdError, LevelPlayAdInfo levelPlayAdInfo) {
                // Ad fails to be displayed
                JSONObject json = new JSONObject();
                try {
                    json.put("error_code", levelPlayAdError.getErrorCode());
                    json.put("msg", levelPlayAdError.getErrorMessage());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                listener.onAdEvent(sdkType(), AdEvent.INTERSTITIAL_AD_SHOW_FAILED, json, -1);
            }
            @Override
            public void onAdClicked(LevelPlayAdInfo levelPlayAdInfo) {
                // Ad was clicked
                listener.onAdEvent(sdkType(), AdEvent.INTERSTITIAL_AD_CLICKED, null, -1);
            }
            @Override
            public void onAdClosed(LevelPlayAdInfo levelPlayAdInfo) {
                // Ad was closed
                SDKManager.removeBuglyUserData("ads");
                listener.onAdEvent(sdkType(), AdEvent.INTERSTITIAL_AD_CLOSED, null, -1);
            }
            @Override
            public void onAdInfoChanged(LevelPlayAdInfo levelPlayAdInfo) {
                // Called after the ad info is updated. Available when another interstitial ad has loaded, and includes a higher CPM/Rate
                // Optional
            }
        };

    @Override
    public void initSDK() {
        LevelPlay.addImpressionDataListener(impressionDataListener);
//        IntegrationHelper.validateIntegration(activity);

        try {
            ApplicationInfo ai = activity.getPackageManager().getApplicationInfo(activity.getPackageName(), PackageManager.GET_META_DATA);
            String appKey = ai.metaData.getString("ironsource_appkey");
            // Init the SDK when implementing the Multiple Ad Units Interstitial and Banner APIs, and Rewarded using legacy APIs
            List<LevelPlay.AdFormat> legacyAdFormats = Arrays.asList();
            assert appKey != null;
            LevelPlayInitRequest initRequest = new LevelPlayInitRequest.Builder(appKey)
                    .withLegacyAdFormats(legacyAdFormats)
                    .withUserId(userID)
                    .build();
            LevelPlayInitListener initListener = new LevelPlayInitListener() {
                @Override
                public void onInitFailed(@NonNull LevelPlayInitError error) {
                    //Recommended to initialize again
                    // 创建失败暂时不处理，因为以前也没处理
                    Log.d(TAG, "onInitFailed");
                }
                @Override
                public void onInitSuccess(LevelPlayConfiguration configuration) {
                    //Create ad objects and load ads
//                    IronSource.launchTestSuite(activity); // log输出各渠道状态信息
                    mRewardedAd = new LevelPlayRewardedAd("imog33d2j9ov1ae2");
                    mRewardedAd.setListener(mLevelPlayRewardedAdListener);
                    mInterstitialAd = new LevelPlayInterstitialAd("md7q01q0a87rimo7");
                    mInterstitialAd.setListener(mLevelPlayInterstitialAdListener);
                    IronsourceManager.mRewardedAd = mRewardedAd;
                    IronsourceManager.mInterstitialAd = mInterstitialAd;
                    IronsourceManager.mIsMultiAdUnitsLoaded = true;

                    listener.onAdEvent(sdkType(), AdEvent.INIT_SDK_FINISHED, null, -1);
                }
            };

//             IronSource.setMetaData("is_test_suite", "enable"); // 弹出test suit 弹窗
            LevelPlay.init(activity, initRequest, initListener);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        inited = true;
        if(addCustomPropertiesTask != null) {
            addCustomPropertiesTask.run();
        }
    }

    @Override
    public void addCustomProperties(JSONObject json) {
        LevelPlaySegment segment = new LevelPlaySegment();
        for (Iterator<String> it = json.keys(); it.hasNext(); ) {
            String key = it.next();
            try {
                String value = String.valueOf(json.get(key));
                segment.setCustom(key, value);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
        addCustomPropertiesTask = new Runnable() {
            @Override
            public void run() {
                LevelPlay.setSegment(segment);
            }
        };
        if(inited) {
            addCustomPropertiesTask.run();
            addCustomPropertiesTask = null;
        }
    }

    @Override
    public void addCustomSegments(JSONObject json) {
        LevelPlaySegment segment = new LevelPlaySegment();
        for (Iterator<String> it = json.keys(); it.hasNext(); ) {
            String key = it.next();
            Log.d(TAG, "key=" + key);
            segment.setSegmentName(key);
        }
        addCustomPropertiesTask = new Runnable() {
            @Override
            public void run() {
                LevelPlay.setSegment(segment);
            }
        };
        if(inited) {
            addCustomPropertiesTask.run();
            addCustomPropertiesTask = null;
        }
    }

    @Override
    public int sdkType() {
        return SDKType.IRONSOURCE;
    }

    @Override
    public boolean isSupportSDKType(int sdkType) {
        return sdkType == sdkType();
    }

    @Override
    public void loadRewardedVideo() {
        if (mRewardedAd != null) {
            mRewardedAd.loadAd();
        }
        listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_AD_LOAD_START, null, -1);
    }

    @Override
    public void loadInterstitialVideo() {
        if (mInterstitialAd != null) {
            mInterstitialAd.loadAd();
        }
        listener.onAdEvent(sdkType(), AdEvent.INTERSTITIAL_AD_LOAD_START, null, -1);
    }

    @Override
    public boolean showRewardedVideo(String placementName) {
        if (mRewardedAd != null && mRewardedAd.isAdReady()) {
            if(TextUtils.isEmpty(placementName)) {
                mRewardedAd.showAd(activity);
            } else if (!LevelPlayRewardedAd.isPlacementCapped(placementName)) {
                mRewardedAd.showAd(activity, placementName);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean isRewardedVideoReady() {
        return mRewardedAd != null && mRewardedAd.isAdReady();
    }

    @Override
    public boolean isInterstitialVideoReady() {
        long current = System.currentTimeMillis();
        if(current - showRewardVideoTS < showInterstitialTimeSpan) {
            return false;
        }
        return mInterstitialAd != null && mInterstitialAd.isAdReady();
    }

    @Override
    public boolean showInterstitial(String placementName) {
        if(mInterstitialAd != null && mInterstitialAd.isAdReady()) {
            if(TextUtils.isEmpty(placementName)) {
                mInterstitialAd.showAd(activity);
            } else if (!LevelPlayInterstitialAd.isPlacementCapped(placementName)){
                mInterstitialAd.showAd(activity, placementName);
            }
            return true;
        }

        return false;
    }

    @Override
    public void uploadPlayerData(JSONObject json) {
        if(json.has("uid")) {
            userID = json.optString("uid", "");
        }
    }

    @Override
    public void onPause() {
//        IronSource.onPause(activity);
    }

    @Override
    public void onResume() {
//        IronSource.onResume(activity);
    }
}
