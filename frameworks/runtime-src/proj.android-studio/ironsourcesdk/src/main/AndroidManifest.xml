<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.frontier.ironsourcesdk">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <application>
        <activity
            android:name="com.ironsource.sdk.controller.ControllerActivity"
            tools:replace="android:theme"
            android:configChanges="orientation|screenSize"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:hardwareAccelerated="true" />
        <activity
            android:name="com.ironsource.sdk.controller.InterstitialActivity"
            tools:replace="android:theme"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.ironsource.sdk.controller.OpenUrlActivity"
            tools:replace="android:theme"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <provider
            android:authorities="${applicationId}.IronsourceLifecycleProvider"
            android:name="com.ironsource.lifecycle.IronsourceLifecycleProvider" />
    </application>
</manifest>