#!/usr/bin/python

android_plist_template = '''
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
        <key>cachedir</key>
        <string>BLDownloadRes</string>
        <key>server</key>
        <string>${server_url}</string>
        <key>port</key>
        <integer>1237</integer>
        <key>logLevel</key>
        <integer>1</integer>
        <key>version</key>
        <string>${version_str}</string>
        <key>apiVersion</key>
        <integer>20171228</integer>
        <key>package</key>
        <string>${package_str}</string>
        <key>FacebookNamespace</key>
        <string>grandegame</string>
        <key>FacebookLink</key>
        <string>268692577001613</string>
        <key>platform</key>
        <string>${plist_platform_identifier}</string>
        <key>res</key>
        <string>res</string>
        <key>versionCode</key>
        <string>${version_integer}</string>
        <key>authoritiesid</key>
        <string>268692577001613</string>
        <key>includedir</key>
        <array>
                <string>Themes</string>
        </array>
        <key>excludefiles</key>
        <array/>
        <key>md5</key>
        <string>${md5_str}</string>
        <key>deeplinkurl</key>
        <string>slots.games.vegas.night.casino</string>
        <key>HELPSHIFT_API_KEY</key>
        <string>fa789de6643ad973f38f4010692c2f30</string>
        <key>HELPSHIFT_DOMAIN</key>
        <string>bolegames.helpshift.com</string>
        <key>HELPSHIFT_APP_ID</key>
        <string>bolegames_platform_20180226132107702-2c981dead95d951</string>
</dict>
</plist>
'''

ios_plist_template = '''
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>cachedir</key>
    <string>BLDownloadRes</string>
    <key>server</key>
    <string>${server_url}</string>
    <key>port</key>
    <integer>1237</integer>
    <key>logLevel</key>
    <integer>1</integer>
    <key>version</key>
    <string>${version_str}</string>
    <key>apiVersion</key>
    <integer>20180102</integer>
    <key>package</key>
    <string>${package_str}</string>
    <key>FacebookNamespace</key>
    <string>slotsdeluxe</string>
    <key>FacebookLink</key>
    <string>268692577001613</string>
    <key>platform</key>
    <string>${plist_platform_identifier}</string>
    <key>res</key>
    <string>res</string>
    <key>includedir</key>
    <array>
        <string>Themes</string>
    </array>
    <key>excludefiles</key>
    <array>
    </array>
    <key>md5</key>
    <string>${md5_str}</string>
    <key>deeplinkurl</key>
    <string>slots.games.vegas.night.casino</string>
    <key>teamid</key>
    <string>Y72H58WM35</string>
</dict>
</plist>
'''

proj_path = '/Users/<USER>/work/Slots888_proj'
# ndk_path = '/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build'
ndk_path = '/Users/<USER>/Library/Android/sdk/ndk/21.4.7075529/ndk-build'
# ndk_path = '/Users/<USER>/Library/Android/sdk/ndk/28.2.13676358/ndk-build'

platform_configs = {
    'android': {
        'plist_template': android_plist_template,
        'version_multiply': 1,
        'md5_str_prefix': 'dafu888_android_'
    },
    'ios': {
        'plist_template': ios_plist_template,
        'version_multiply': 10,
        'md5_str_prefix': 'dafu888_ios_'
    }
}

server_url = {
    'rtm': 'http://***********:1300/sea/dafu888/',
    'online': 'https://d105xpbtjj9cjp.cloudfront.net/sea/dafu888/'
}

package_configs = {
    'google': {
        'package': 'com.grandegames.slots.dafu.casino',
        'plist_platform_identifier': 'android',
        'version_multiply' : 1,
        'md5_prefix': 'dafu888_android',
        'platform': 'android'
    },
    'amazon': {
        'package': 'com.grandegames.slots.dafu.casino.amazon',
        'plist_platform_identifier': 'amazon',
        'version_multiply': 1,
        'md5_prefix': 'dafu888_android',
        'platform': 'android'
    },
    'ios': {
        'package': 'slots.games.vegas.night.casino',
        'plist_platform_identifier': 'ios',
        'version_multiply': 10,
        'md5_prefix': 'dafu888_ios',
        'platform': 'ios'
    }
}
