package com.frontier.googleaccountsdk;

import android.app.Activity;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.frontier.sdkbase.AbstractAccountImpl;
import com.frontier.sdkbase.ErrorCode;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKType;
import com.google.android.gms.auth.api.signin.GoogleSignIn;
import com.google.android.gms.auth.api.signin.GoogleSignInAccount;
import com.google.android.gms.auth.api.signin.GoogleSignInClient;
import com.google.android.gms.auth.api.signin.GoogleSignInOptions;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.games.Games;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;

public class GoogleAccountHelper extends AbstractAccountImpl {

    private static final int RC_SIGN_IN = 124453;
    private GoogleSignInClient googleSignInClient;
    private Boolean isLogin = false;
    private GoogleSignInAccount signedInAccount;

    public GoogleAccountHelper(Activity activity, EventListener listener) {
        super(activity, listener);
        GoogleSignInOptions signInOptions = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
//                .requestScopes(Games.SCOPE_GAMES_SNAPSHOTS)
                .build();
        googleSignInClient = GoogleSignIn.getClient(activity, signInOptions);
    }

    @Override
    public void login(int sdkType) {
        if(isLogin) {
            onLoginSuccess();
        } else {
            googleSignInClient.silentSignIn().addOnCompleteListener(new OnCompleteListener<GoogleSignInAccount>() {
                @Override
                public void onComplete(Task<GoogleSignInAccount> task) {
                    if(task.isSuccessful()) {
                        GoogleSignInAccount account = task.getResult();
                        if(account != null) {
                            signedInAccount = account;
                            onLoginSuccess();
                        } else {
                            activity.startActivityForResult(googleSignInClient.getSignInIntent(), RC_SIGN_IN);
                        }
                    } else {
                        activity.startActivityForResult(googleSignInClient.getSignInIntent(), RC_SIGN_IN);
                    }
                }
            });
        }
    }

    @Override
    public void logout(int sdkType) {
        isLogin = false;
        googleSignInClient.signOut().addOnCompleteListener(activity, new OnCompleteListener<Void>() {
            @Override
            public void onComplete(Task<Void> task) {
                listener.onLogout(sdkType(), ErrorCode.NO_ERROR, null, 0);
            }
        });
    }

    @Override
    public boolean isLogin() {
        return isLogin;
    }

    @Override
    public String getID() {
        if(signedInAccount == null) {
            return "";
        }
        return signedInAccount.getId();
    }

    @Override
    public String getName() {
        if(signedInAccount == null) {
            return "";
        }
        return signedInAccount.getDisplayName();
    }

    @Override
    public String getToken() {
        if(signedInAccount == null) {
            return "";
        }
        return signedInAccount.getIdToken();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if(requestCode == RC_SIGN_IN) {
            Task<GoogleSignInAccount> task = GoogleSignIn.getSignedInAccountFromIntent(data);
            try {
                signedInAccount = task.getResult(ApiException.class);
                onLoginSuccess();
            } catch (ApiException e) {
                e.printStackTrace();
                onLoginFailed(ErrorCode.COMMON_ERROR);
            }
        }
    }

    @Override
    public int sdkType() {
        return SDKType.GOOGLE;
    }

    @Override
    public boolean isSupportSDKType(int sdkType) {
        return sdkType == sdkType();
    }

    private void onLoginSuccess() {
        isLogin = true;
        String photoUrl = "";
        if(signedInAccount.getPhotoUrl() != null) {
            photoUrl = signedInAccount.getPhotoUrl().toString();
        }
        listener.onLogin(sdkType(),
                ErrorCode.NO_ERROR,
                signedInAccount.getId(),
                signedInAccount.getDisplayName(),
                signedInAccount.getIdToken(),
                photoUrl,
                signedInAccount.getEmail(),
                null,
                0);
    }

    private void onLoginFailed(int code) {
        isLogin = false;
        listener.onLogin(sdkType(),
                code,
                "",
                "",
                "",
                "",
                "",
                null,
                0);
    }
}
