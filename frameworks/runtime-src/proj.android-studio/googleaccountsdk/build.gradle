plugins {
    id 'com.android.library'
}

android {
    namespace "com.frontier.googleaccountsdk"
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'com.google.android.material:material:1.3.0'
    implementation "com.google.android.gms:play-services-games:20.0.1"
    implementation "com.google.android.gms:play-services-auth:19.0.0"

    implementation project(':sdkbase')
}
