# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.jvmargs=-Xmx4096m

# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
PROP_APP_ABI=armeabi:armeabi-v7a:arm64-v8a
android.useAndroidX=true
android.enableJetifier=true

# uncomment it and fill in sign information for release mode
#RELEASE_STORE_FILE=
#RELEASE_STORE_PASSWORD=
#RELEASE_KEY_ALIAS=
#RELEASE_KEY_PASSWORD=

# if you want to encrypt lua codes, uncomment the codes and set value to PROP_LUA_ENCRYPT_KEY and PROP_LUA_ENCRYPT_SIGN
#PROP_LUA_ENCRYPT=1
#PROP_LUA_ENCRYPT_KEY=
#PROP_LUA_ENCRYPT_SIGN=
PROP_TARGET_SDK_VERSION=28
