include ':googlepaysdk'
include ':hwsdk'
include ':libcocos2dx'
project(':libcocos2dx').projectDir = new File(settingsDir, '../../cocos2d-x/cocos/platform/android/libcocos2dx')

include ':Slots'
project(':Slots').projectDir = new File(settingsDir, 'app')

include ':sdkbase'

include ':comonprotocol'
project(':comonprotocol').projectDir = new File(settingsDir, '../../cocos2d-x/plugin/protocols/comonprotocol')

include ':pluginhelpshift'
project(':pluginhelpshift').projectDir = new File(settingsDir, '../../cocos2d-x/plugin/protocols/pluginhelpshift')

include ':pluginfacebook'
project(':pluginfacebook').projectDir = new File(settingsDir, '../../cocos2d-x/plugin/protocols/pluginfacebook')

include ':pluginGoolgeIAP'
project(':pluginGoolgeIAP').projectDir = new File(settingsDir, '../../cocos2d-x/plugin/protocols/pluginGoolgeIAP')

include ':pluginfirebase'
project(':pluginfirebase').projectDir = new File(settingsDir, '../../cocos2d-x/plugin/protocols/pluginfirebase')

include ':pluginmopub'
project(':pluginmopub').projectDir = new File(settingsDir, '../../cocos2d-x/plugin/protocols/pluginmopub')
include ':googleaccountsdk'
include ':googlenpgaccountsdk'
include ':ironsourcesdk'
include ':GoogleExtraLibs'
include ':twittersdk'
include ':jw_res'
include ':samsungpaysdk'
include ':onestorepaysdk'
include ':sharesdk'
include ':samsungaccountsdk'
include ':samsungclouddevsdk'
include ':samsungadssdk'
include ':odeeosdk'
include ':admodsdk'
