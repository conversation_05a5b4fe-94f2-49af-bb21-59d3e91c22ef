<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>VIP-icon-Lv01.png</key>
            <dict>
                <key>frame</key>
                <string>{{79,1},{76,70}}</string>
                <key>offset</key>
                <string>{0,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{12,18},{76,70}}</string>
                <key>sourceSize</key>
                <string>{100,100}</string>
            </dict>
            <key>VIP-icon-Lv02.png</key>
            <dict>
                <key>frame</key>
                <string>{{157,1},{76,70}}</string>
                <key>offset</key>
                <string>{0,-3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{12,18},{76,70}}</string>
                <key>sourceSize</key>
                <string>{100,100}</string>
            </dict>
            <key>VIP-icon-Lv03.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,69},{76,70}}</string>
                <key>offset</key>
                <string>{0,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{12,18},{76,70}}</string>
                <key>sourceSize</key>
                <string>{100,100}</string>
            </dict>
            <key>VIP-icon-Lv04.png</key>
            <dict>
                <key>frame</key>
                <string>{{79,73},{76,70}}</string>
                <key>offset</key>
                <string>{0,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{12,18},{76,70}}</string>
                <key>sourceSize</key>
                <string>{100,100}</string>
            </dict>
            <key>VIP-icon-Lv05.png</key>
            <dict>
                <key>frame</key>
                <string>{{157,79},{76,70}}</string>
                <key>offset</key>
                <string>{0,-3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{12,18},{76,70}}</string>
                <key>sourceSize</key>
                <string>{100,100}</string>
            </dict>
            <key>VIP-icon-Lv06.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,141},{76,70}}</string>
                <key>offset</key>
                <string>{0,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{12,18},{76,70}}</string>
                <key>sourceSize</key>
                <string>{100,100}</string>
            </dict>
            <key>VIP-icon-Lv07.png</key>
            <dict>
                <key>frame</key>
                <string>{{79,145},{76,70}}</string>
                <key>offset</key>
                <string>{0,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{12,18},{76,70}}</string>
                <key>sourceSize</key>
                <string>{100,100}</string>
            </dict>
            <key>VIP-icon-Lv08.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,1},{76,66}}</string>
                <key>offset</key>
                <string>{-1,-1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,4},{76,66}}</string>
                <key>sourceSize</key>
                <string>{78,72}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>VIP_icon_Lv.png</string>
            <key>size</key>
            <string>{228,216}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:dd1ddf5470d68399f246383ded798d15:30f30e6ca5db84f540abb20ea4018a5f:eea24511ac9cbbf2035801b851d633f6$</string>
            <key>textureFileName</key>
            <string>VIP_icon_Lv.png</string>
        </dict>
    </dict>
</plist>
