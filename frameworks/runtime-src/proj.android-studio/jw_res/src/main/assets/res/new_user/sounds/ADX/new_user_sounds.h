﻿/*===========================================================================*
 *  Header file for Atom CueSheet Binary
 *  Project          : themeAdx
 *  Tool Ver.        : Ver.3.52.03
 *  ACB  Ver.        : Ver.1.42.1
 *  File Path        : /Users/<USER>/Slots8881/Client/themeAdx/output/PC/gx
 *  File Name        : new_user_sounds.h
 *  File Size        : 9248 bytes
 *  Date Time        : 2025/06/25 20:08:32
 *  Target           : PC
 *  Cues             : 22
 *  CueSheet Comment : 
 *  Stream Awb Path  : /Users/<USER>/Slots8881/Client/themeAdx/output/PC/gx/new_user_sounds.awb
 *===========================================================================*/

#define CRI_NEW_USER_SOUNDS_CUENUM (22)

/* AISAC Control List (AISAC Control ID) */

/* Cue List (Cue ID) */
#define CRI_NEW_USER_SOUNDS_DIAMOND_MAN_ENTER    ( 0) /*  */
#define CRI_NEW_USER_SOUNDS_DIAMOND_MAN_ENTER_NEW ( 1) /*  */
#define CRI_NEW_USER_SOUNDS_DIAMOND_MAN_EXIT     ( 2) /*  */
#define CRI_NEW_USER_SOUNDS_DIAMOND_MAN_HAND_CLICK ( 3) /*  */
#define CRI_NEW_USER_SOUNDS_DIAMOND_MAN_OPEN_BOX ( 4) /*  */
#define CRI_NEW_USER_SOUNDS_DIAMOND_MAN_TRANSFORM ( 5) /*  */
#define CRI_NEW_USER_SOUNDS_PLAY_CN              ( 6) /*  */
#define CRI_NEW_USER_SOUNDS_PLAY_EN              ( 7) /*  */
#define CRI_NEW_USER_SOUNDS_PLAY_TW              ( 8) /*  */
#define CRI_NEW_USER_SOUNDS_PLAY_DIAMONDMAN_CN   ( 9) /*  */
#define CRI_NEW_USER_SOUNDS_PLAY_DIAMONDMAN_EN   (10) /*  */
#define CRI_NEW_USER_SOUNDS_PLAY_DIAMONDMAN_TW   (11) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_CN           (12) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_EN           (13) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_TW           (14) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_DIAMONDMAN_CN (15) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_DIAMONDMAN_EN (16) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_DIAMONDMAN_JA (17) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_DIAMONDMAN_TW (18) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_DIAMONDMAN_NEW_CN (19) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_DIAMONDMAN_NEW_EN (20) /*  */
#define CRI_NEW_USER_SOUNDS_WELCOME_DIAMONDMAN_NEW_JA (21) /*  */

/* Block List (Block Index) */

/* end of file */

