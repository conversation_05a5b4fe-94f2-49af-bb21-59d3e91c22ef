<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>coin_00.png</key>
            <dict>
                <key>frame</key>
                <string>{{4,124},{114,114}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{18,18},{114,114}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_01.png</key>
            <dict>
                <key>frame</key>
                <string>{{120,364},{110,114}}</string>
                <key>offset</key>
                <string>{4,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{24,18},{110,114}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_02.png</key>
            <dict>
                <key>frame</key>
                <string>{{236,230},{98,116}}</string>
                <key>offset</key>
                <string>{8,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{34,17},{98,116}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_03.png</key>
            <dict>
                <key>frame</key>
                <string>{{320,352},{78,116}}</string>
                <key>offset</key>
                <string>{8,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{44,17},{78,116}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_04.png</key>
            <dict>
                <key>frame</key>
                <string>{{448,180},{48,118}}</string>
                <key>offset</key>
                <string>{6,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{57,16},{48,118}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_05.png</key>
            <dict>
                <key>frame</key>
                <string>{{128,484},{20,118}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{65,16},{20,118}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_06.png</key>
            <dict>
                <key>frame</key>
                <string>{{394,180},{48,118}}</string>
                <key>offset</key>
                <string>{-7,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{44,16},{48,118}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_07.png</key>
            <dict>
                <key>frame</key>
                <string>{{236,352},{78,116}}</string>
                <key>offset</key>
                <string>{-9,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{27,17},{78,116}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_08.png</key>
            <dict>
                <key>frame</key>
                <string>{{228,4},{98,116}}</string>
                <key>offset</key>
                <string>{-8,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{18,17},{98,116}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_09.png</key>
            <dict>
                <key>frame</key>
                <string>{{120,244},{110,114}}</string>
                <key>offset</key>
                <string>{-4,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{16,18},{110,114}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{4,4},{114,114}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{18,18},{114,114}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{4,364},{110,114}}</string>
                <key>offset</key>
                <string>{4,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{24,18},{110,114}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,126},{98,116}}</string>
                <key>offset</key>
                <string>{8,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{34,17},{98,116}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_13.png</key>
            <dict>
                <key>frame</key>
                <string>{{332,4},{78,116}}</string>
                <key>offset</key>
                <string>{8,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{44,17},{78,116}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_14.png</key>
            <dict>
                <key>frame</key>
                <string>{{368,126},{48,118}}</string>
                <key>offset</key>
                <string>{6,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{57,16},{48,118}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_15.png</key>
            <dict>
                <key>frame</key>
                <string>{{4,484},{20,118}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{65,16},{20,118}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_16.png</key>
            <dict>
                <key>frame</key>
                <string>{{340,210},{48,118}}</string>
                <key>offset</key>
                <string>{-7,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{44,16},{48,118}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_17.png</key>
            <dict>
                <key>frame</key>
                <string>{{246,126},{78,116}}</string>
                <key>offset</key>
                <string>{-9,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{27,17},{78,116}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_18.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,4},{98,116}}</string>
                <key>offset</key>
                <string>{-8,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{18,17},{98,116}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
            <key>coin_19.png</key>
            <dict>
                <key>frame</key>
                <string>{{4,244},{110,114}}</string>
                <key>offset</key>
                <string>{-4,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{16,18},{110,114}}</string>
                <key>sourceSize</key>
                <string>{150,150}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>coin.png</string>
            <key>size</key>
            <string>{512,512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:72367fc2c773a3b0bc918b07224f02de:1/1$</string>
            <key>textureFileName</key>
            <string>coin.png</string>
        </dict>
    </dict>
</plist>
