<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/backgroundBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/push_background" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/notificaionicon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:scaleType="fitCenter"
            android:src="@drawable/notification_icon" />

        <RelativeLayout
            android:id="@+id/relativeLayout1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1065353216.000000"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:singleLine="true"
                android:text="Slot Deluxe"
                android:textAppearance="@style/TextAppearance.AppCompat" />

            <TextView
                android:id="@+id/text2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_below="@id/text1"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="10dp"
                android:layout_marginTop="-5dp"
                android:ellipsize="middle"
                android:lines="2"
                android:text="Hello World!!"
                android:textAppearance="@style/TextAppearance.AppCompat" />
        </RelativeLayout>
    </LinearLayout>
</RelativeLayout>
