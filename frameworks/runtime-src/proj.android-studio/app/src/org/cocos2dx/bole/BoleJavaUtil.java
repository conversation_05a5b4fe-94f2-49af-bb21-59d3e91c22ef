package org.cocos2dx.bole;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Parcelable;
import android.preference.PreferenceManager;
import android.provider.MediaStore;
import android.provider.Settings.Secure;
import android.telephony.TelephonyManager;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;

import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustAdRevenue;
import com.adjust.sdk.AdjustAttribution;
import com.adjust.sdk.AdjustConfig;
import com.adjust.sdk.AdjustEvent;
import com.adjust.sdk.AdjustThirdPartySharing;
import com.adjust.sdk.OnAttributionReadListener;
import com.google.android.gms.ads.identifier.AdvertisingIdClient;
import com.google.android.gms.ads.identifier.AdvertisingIdClient.Info;

import org.cocos2dx.bole.notification.BoleNotification;
import org.cocos2dx.bole.notification.BootReceiver;
import org.cocos2dx.fcm.FireBaseManager;
import org.cocos2dx.lib.Cocos2dxActivity;
import org.cocos2dx.lib.Cocos2dxHelper;
import org.cocos2dx.lua.MemoryMonitor;
import org.json.JSONException;
import org.json.JSONObject;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.io.File;
import android.os.Environment;
import android.os.StatFs;
import org.cocos2dx.lua.BoleApplication;

public class BoleJavaUtil {
	static String tag = "bole";

	public static String getMacID(){
		Activity a = Cocos2dxHelper.getActivity();
		if (a != null) {
			WifiManager wifiMan = (WifiManager) a.getSystemService(Context.WIFI_SERVICE);
            if (wifiMan != null) {
                WifiInfo wifiInf = wifiMan.getConnectionInfo();
            	return wifiInf.getMacAddress() ;
            }
		}
		Log.w(tag, "Failed to get mac id");
		return "";
	}
	
	public static String getReferrer() {
		Activity a = Cocos2dxHelper.getActivity();
		if ( a != null) {
			String referrer = a.getSharedPreferences(ReferrerReceiver.preferences, Context.MODE_PRIVATE).getString(ReferrerReceiver.sharedKey,null);
            if (referrer!=null)
            	return referrer;
		}
		Log.w(tag, "Failed to get referrer");
		return "";
	}

	public static void keepScreenOn(final boolean bKeepOn) {
		final Activity a = Cocos2dxHelper.getActivity();
		if (a != null) {
			a.runOnUiThread(new Runnable() {
				@Override
				public void run() {
					if (bKeepOn) {
						a.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
					} else {
						a.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
					}
				}
			});
		}
		Log.w(tag, "Failed to get referrer");
	}
	
	public static String getIMEI(){
		Activity a = Cocos2dxHelper.getActivity();
		if ( a != null) {
			TelephonyManager telephonyManager = (TelephonyManager) a.getSystemService(Context.TELEPHONY_SERVICE);
		    String imei = telephonyManager.getDeviceId();
		    return imei;
		}
		Log.w(tag, "Failed to get imei");
		return "";
	}

	public static String getDeviceId(){
		Activity a = Cocos2dxHelper.getActivity();
		if ( a != null) {
		    String deviceId = Secure.getString(a.getContentResolver(),Secure.ANDROID_ID);
		    return deviceId;
		}
		Log.w(tag, "Failed to get deviceId");
		return "";
	}
	
	public static String addNotification(String title, String desc, int seconds){
		return BoleNotification.addNotification(title, desc, seconds);
	}

	//Bing change GCM - > FCM
	public static String registerGoogle( ){
		return FireBaseManager.register();
	}
	
	public static void removeGoogleRegistrationId( ){
		FireBaseManager.removeRegistrationId();
	}

	public static void subscribeToTopic(String topic){
		FireBaseManager.subscribeToTopic(topic);
	}
	public static void unsubscribeFromTopic(String topic){
		FireBaseManager.unsubscribeFromTopic(topic);
	}
	//end FCM


	//FirebaseCrash.log
	public static void CrashLog(String errorMsg){
//		FirebaseCrash.log(errorMsg);
	}

	
	public static int getResId(String name, String type) {
		Activity activity = Cocos2dxHelper.getActivity();
		return activity.getResources().getIdentifier(name, type, activity.getPackageName());
	}
	
	public static void addShortCut() {
		Activity a = Cocos2dxHelper.getActivity();
		SharedPreferences appPreferences = PreferenceManager.getDefaultSharedPreferences(a);
	    Boolean isAppInstalled = appPreferences.getBoolean("isAppInstalled", false);
		if (! isAppInstalled) {
			Intent shortcutintent = new Intent("com.android.launcher.action.INSTALL_SHORTCUT");
			shortcutintent.putExtra("duplicate", false);
			shortcutintent.putExtra(Intent.EXTRA_SHORTCUT_NAME, a.getString(getResId("app_name", "string")));
			Parcelable icon = Intent.ShortcutIconResource.fromContext(a.getApplicationContext(), getResId("icon", "drawable"));
			shortcutintent.putExtra(Intent.EXTRA_SHORTCUT_ICON_RESOURCE, icon);
			shortcutintent.putExtra(Intent.EXTRA_SHORTCUT_INTENT,new Intent(a.getApplicationContext(), a.getClass()));
			a.sendBroadcast(shortcutintent);
			SharedPreferences.Editor editor = appPreferences.edit();
		    editor.putBoolean("isAppInstalled", true);
		    editor.commit();
		}
	}
	
	public static String setBootNotification(String title, String desc, int chancePercent){
		return BootReceiver.setBootNotification(title, desc, chancePercent);
	}
	
	public static String clearAllNotifications(){
		BoleNotification.clearNotifications();
		return "success";
	}
	public static String getCountry(){
		String country= Cocos2dxHelper.getActivity().getResources().getConfiguration().locale.getCountry();
		return country;
	}
	
	public static String getPhoneNumber(){ 
	    TelephonyManager mTelephonyMgr;
	    try{
		    mTelephonyMgr = (TelephonyManager)  Cocos2dxHelper.getActivity().getSystemService(Context.TELEPHONY_SERVICE);    
		    return mTelephonyMgr.getLine1Number();
	    } catch (Exception ex) {
	    }
	    return "";
	}
	
		//return device info by index
        //0 device Modele
        //1 packagename
        //2 displayname appname
        //3 version
	public static String getDeviceInfo(int index){
		String ret = "";

		PackageManager packageManager = Cocos2dxHelper.getActivity().getPackageManager();
        ApplicationInfo applicationInfo = null;
        PackageInfo info = null;
        try {
            applicationInfo = packageManager.getApplicationInfo(Cocos2dxHelper.getActivity().getApplicationInfo().packageName, 0);
            info = packageManager.getPackageInfo(Cocos2dxHelper.getActivity().getApplicationInfo().packageName, 0);
        } catch (final PackageManager.NameNotFoundException e) {
            Log.w(tag,e.toString());
        }

		switch(index){
			case 0:
				ret = android.os.Build.BRAND + ' ' + android.os.Build.MODEL;
				break;
			case 1:
				ret = info.packageName; //getPackageName();
				break;
			case 2:
				ret =  (String) (applicationInfo != null ? packageManager.getApplicationLabel(applicationInfo) : "Unknown");
				break;
			case 3:
				ret = info.versionName;
				break;
			default:
				break;
		}
		return ret;
	}

	public static String getSysVersion() {
		return android.os.Build.VERSION.RELEASE;
	}

	static String mAdId = "";
	static Boolean mIsRequireADID = false;
	// Do not call this function from the main thread. Otherwise,
	// an IllegalStateException will be thrown.
	// 异步获取广告id(防止卡住，影响后续逻辑或产生ANR)，不会立即返回。如果之前有存储可以直接返回。
	public static String getAdvertisingId() {
		if (mAdId != null && !mAdId.equals("")){
			return mAdId;
		}

		if (mIsRequireADID){
			Log.i(tag, "getAdvertisingId is requiring");
			return "";
		}

		new Thread(new Runnable() {
			@Override
			public void run() {
				int loopIndex = 0;
				mIsRequireADID = true;
				while (loopIndex < 10){
					loopIndex ++;

					Info adInfo = null;
					try {
						adInfo = AdvertisingIdClient.getAdvertisingIdInfo(Cocos2dxHelper.getActivity());
					} catch (IOException e) {
						Log.i(tag, "getAdvertisingId IOException");
					} catch (Exception e) {
						Log.i(tag, "getAdvertisingId Exception");
					}
					if (adInfo != null){
						Log.i(tag, "getAdvertisingId success");
						mAdId = adInfo.getId();
						break;
					}
				}
				mIsRequireADID =false;
			}
		}).start();

		return "";
	}
	
	public static String getCurrentTimeZone()  
	{  
	    TimeZone tz = TimeZone.getDefault();  
	    return tz.getDisplayName(false, TimeZone.SHORT);
	}  
	
	/**
     * Get IP address from first non-localhost interface
     * @param ipv4  true=return ipv4, false=return ipv6
     * @return  address or empty string
     */
    public static String getIPAddress(/*boolean useIPv4*/) {
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface intf : interfaces) {
                List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
                for (InetAddress addr : addrs) {
                    if (!addr.isLoopbackAddress()) {
                        String sAddr = addr.getHostAddress();

                        if (sAddr.indexOf("0.0.") >= 0)
                        	continue;
                        if (sAddr.indexOf("192.168.") >= 0)
                        	continue;
                        
                        //boolean isIPv4 = InetAddressUtils.isIPv4Address(sAddr);
                        boolean isIPv4 = sAddr.indexOf(':') < 0;

//                        if (useIPv4) {
                            if (isIPv4)
                                return sAddr;
//                        } else {
                            if (!isIPv4) {
                                int delim = sAddr.indexOf('%'); // drop ip6 zone suffix
                                return delim<0 ? sAddr.toUpperCase() : sAddr.substring(0, delim).toUpperCase();
                            }
//                        }
                    }
                }
            }
        } catch (Exception ex) { } // for now eat exceptions
        return "";
    }
    
	public static String savePicToMediaStore(byte[] data,String title, String desc){
		Activity a = Cocos2dxHelper.getActivity();
		Bitmap bm = BitmapFactory.decodeByteArray(data, 0, data.length); 
        String url= MediaStore.Images.Media.insertImage(a.getContentResolver(), bm, title, desc);
        return url;
	}
	public static void deletePicFromMediaStore(String file){
		Activity a = Cocos2dxHelper.getActivity();
		a.getContentResolver().delete(Uri.parse(file), null, null);
	}
	
	private static HashMap<String, Object> config=null;
	public static HashMap<String, Object> getConfig(Context context) {
		if(config!=null)
			return config;
		config = new HashMap<String, Object>();
		try{
			XmlPullParserFactory factory = XmlPullParserFactory.newInstance();
	        factory.setNamespaceAware(true);
	        String fileData = "";
	        String line = "";
	        BufferedReader br=new BufferedReader(new InputStreamReader(context.getApplicationContext().getResources().getAssets().open("src/config.plist")));
	        while((line = br.readLine()) != null)
	        {
	            fileData += line.replaceAll("[\n\r]", "").trim();
	        }
	        XmlPullParser parser = factory.newPullParser();
	        parser.setInput(new StringReader(fileData));
	//	         parser.setInput(getApplicationContext().getResources().getAssets().open("config.plist"), "utf-8");
			final String KEY = "key", STRING = "string", ARRAY="array",INTEGER="integer";
			
			try {
				parser.next();
				int eventType = parser.getEventType();
				String lastTag = null;
				String lastKey = null;
				boolean inarray = false;
				while (eventType != XmlPullParser.END_DOCUMENT) {
					if (eventType == XmlPullParser.START_TAG) {
						lastTag = parser.getName();
						if (ARRAY.equalsIgnoreCase(lastTag)){
							config.put(lastKey, new ArrayList<Object>());
						}
					}
					else if (eventType == XmlPullParser.TEXT) {
						// some text
						if (KEY.equalsIgnoreCase(lastTag)) {
							// start tracking a new key
							String text = parser.getText().trim();
							if(text!=null && !text.isEmpty() && text.length()>0)
								lastKey = text;
						}
						else if (STRING.equalsIgnoreCase(lastTag)) {
							// a new string for the last encountered key
							String value= parser.getText().trim(); 
							if (config.containsKey(lastKey) && config.get(lastKey) instanceof ArrayList){
								((ArrayList<Object>) config.get(lastKey)).add(value);
							}
							else
								config.put(lastKey, value);
						}
						else if (INTEGER.equalsIgnoreCase(lastTag)){
							Integer v = new Integer(parser.getText().trim());
							if (config.containsKey(lastKey) && config.get(lastKey) instanceof ArrayList){
								((ArrayList<Object>) config.get(lastKey)).add(v);
							}
							else
								config.put(lastKey, v);
						}
					}
					eventType = parser.next();
				}
			} catch (XmlPullParserException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		catch (Exception e) {
			Log.i(tag,"failed to parse:"+e.toString());
		}
		return config;
	}

    public static void crashTest() {
		throw new RuntimeException("This is a crash");
//		CrashReport.testNativeCrash();
    }

	public static void testSeriousBlock() {
		Cocos2dxHelper.getActivity().runOnUiThread(new Runnable() {
			@Override
			public void run() {
				try {
					Thread.sleep(3500);
				} catch (Exception ignored){
				}
			}
		});
	}

	public static void adjustTrackEvent(String data) {
		Log.i("AdjustAPI", "adjustTrackEvent: " + data);
		try {
			JSONObject json = new JSONObject(data);
			String token = json.getString("event");
			AdjustEvent event = new AdjustEvent(token);
			for (Iterator<String> it = json.keys(); it.hasNext(); ) {
				String key = it.next();
				if(key.equals("event")) {
					continue;
				}
				 event.addCallbackParameter(key, json.getString(key));
			}
			Adjust.trackEvent(event);
		} catch (JSONException e) {
			e.printStackTrace();
		}
	}

	public static void adjustTrackRevenue(String eventToken, String data, String unused) {
		Log.i("AdjustAPI", "adjustTrackRevenue: " + eventToken + ", data = " + data);
		AdjustEvent event = new AdjustEvent(eventToken);
		try {
			JSONObject json = new JSONObject(data);
			event.setRevenue(json.getDouble("revenue"), json.getString("currency"));
			for (Iterator<String> it = json.keys(); it.hasNext(); ) {
				String key = it.next();
				if(key.equals("event") || key.equals("revenue") || key.equals("currency")) {
					continue;
				}
				if(key.equals("order_id")) {
					// event.setOrderId(json.getString(key));
					event.setDeduplicationId(json.getString(key));
				} else {
					 event.addCallbackParameter(key, json.getString(key));
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		}
		Adjust.trackEvent(event);
	}

	public static void tictokTrackEvent(String data) {
		Log.i("TictokAPI", "tictokTrackEvent: " + data);
//		try {
//			JSONObject json = new JSONObject(data);
//			String token = json.getString("event");
//			TTBaseEvent.Builder testBuilder = TTBaseEvent.newBuilder(token); //event name
//			TTBaseEvent testInfo;
//			for (Iterator<String> it = json.keys(); it.hasNext(); ) {
//				String key = it.next();
//				if(key.equals("event")) {
//					continue;
//				}
//				testBuilder = testBuilder.addProperty(key, json.getString(key));
//			}
//			testInfo = testBuilder.build();
//			TikTokBusinessSdk.trackTTEvent(testInfo);
//		} catch (JSONException e) {
//			e.printStackTrace();
//		}
	}

	public static void tictokTrackRevenue(String eventToken, String data){
		Log.i("TictokAPI", "tictokTrackRevenue: " + data);
//		try {
//			TTBaseEvent testInfo;
//			JSONObject json = new JSONObject(data);
//			TTBaseEvent.Builder testBuilder = TTBaseEvent.newBuilder(eventToken);
//			// 跟踪自定义事件
//			testBuilder = testBuilder.addProperty("currency", json.getString("currency")); //The ISO 4217 currency code
//			testBuilder = testBuilder.addProperty("value", json.getDouble("revenue")); // Value of the order or items sold
//			testBuilder = testBuilder.addProperty("price", json.getDouble("revenue"));
//			for (Iterator<String> it = json.keys(); it.hasNext(); ) {
//				String key = it.next();
//				if(key.equals("event") || key.equals("revenue") || key.equals("currency")) {
//					continue;
//				}
//				if(key.equals("order_id")) {
//					testBuilder = testBuilder.addProperty("content_id", json.getString("order_id"));//Unique ID of the product or content
//				} else {
//					testBuilder = testBuilder.addProperty(key, json.getString(key));
//				}
//			}
//			testInfo = testBuilder.build();
//			TikTokBusinessSdk.trackTTEvent(testInfo);
//		} catch (JSONException e) {
//			e.printStackTrace();
//		}
	}

	public static void adjustTrackAdRevenue(String impressionData) {
		Log.i("AdjustAPI", "adjustTrackAdRevenue: " + impressionData);
		try {
			JSONObject json = new JSONObject(impressionData);
			// AdjustAdRevenue adjustAdRevenue = new AdjustAdRevenue(AdjustConfig.AD_REVENUE_IRONSOURCE);
			AdjustAdRevenue adjustAdRevenue = new AdjustAdRevenue("ironsource_sdk");
			adjustAdRevenue.setRevenue(json.getDouble("revenue"), "USD");
			adjustAdRevenue.setAdRevenueNetwork(json.optString("adnetwork", ""));
			adjustAdRevenue.setAdRevenueUnit(json.optString("adunit", ""));
			adjustAdRevenue.setAdRevenuePlacement(json.optString("placement", ""));
			Adjust.trackAdRevenue(adjustAdRevenue);
		} catch (JSONException e) {
			e.printStackTrace();
		}
	}

	public static String adjustGetAttribution() {
		String res = BoleApplication.getAttribution();
		if(res != null){
			Log.i("AdjustAPI", "adjustGetAttribution log:"+res.toString());
			return res;
		}else{
			return "";
		}
	}

	public static void adjustTrackThirdPartySharing(String jsonStr) {
		try {
			Log.d(tag, String.format("adjustTrackThirdPartySharing jsonStr=%s", jsonStr));
			JSONObject obj  = new JSONObject(jsonStr);
			String eeaStr = obj.getString("eea");
			String adpStr = obj.getString("ad_personalization");
			String aduStr = obj.getString("ad_user_data");
			AdjustThirdPartySharing adjustThirdPartySharing = new AdjustThirdPartySharing(null);
			adjustThirdPartySharing.addGranularOption("google_dma", "eea", eeaStr);
			adjustThirdPartySharing.addGranularOption("google_dma", "ad_personalization", adpStr);
			adjustThirdPartySharing.addGranularOption("google_dma", "ad_user_data", aduStr);
			Adjust.trackThirdPartySharing(adjustThirdPartySharing);
		} catch (JSONException e) {
			e.printStackTrace();
		}
	}

	public static void firebaseLogEvent(String eventName, String otherInfo) {
		FireBaseManager.logEvent(eventName, otherInfo);
	}

	public static void firebaseFetchRemoteConfig() {
		FireBaseManager.fetchRemoteConfig(null);
	}

	public static void firebaseFetchRemoteConfig(String jsonData) {
		FireBaseManager.fetchRemoteConfig(jsonData);
	}
	public static String firebaseGetRCValueStringForKey(String key) {
		return FireBaseManager.getRCValueStringForKey(key);
	}
	public static double firebaseGetRCValueNumberForKey(String key) {
		return FireBaseManager.getRCValueNumberForKey(key);
	}
	public static void firebaseSetConsent(boolean flag1, boolean flag2, boolean flag3, boolean flag4) {
		Log.d(tag, String.format("firebaseSetConsent flag1=%b,flag2=%b,flag3=%b,flag4=%b", flag1, flag2, flag3, flag4));
		Map<String, Boolean> map = new HashMap<>();
		map.put("ANALYTICS_STORAGE", flag1);
		map.put("AD_STORAGE", flag2);
		map.put("AD_USER_DATA", flag3);
		map.put("AD_PERSONALIZATION", flag4);
		FireBaseManager.setConsent(map);
	}
	 public static int getFreeStorage(){
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long freeBlocks = stat.getAvailableBlocks();
        long ret = freeBlocks*blockSize/1000000;
        return (int)ret;
    }
    public static int getTotalStorage(){
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long totalBlocks = stat.getBlockCount();
        long ret = totalBlocks*blockSize/1000000;
        return (int)ret;
    }

	// 设备总内存
	public static int getMemoryTotal(){
		Activity a = Cocos2dxHelper.getActivity();
		if ( a != null) {
			ActivityManager manager = (ActivityManager) a.getSystemService(Context.ACTIVITY_SERVICE);
			ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
			manager.getMemoryInfo(mi);
			return (int)(mi.totalMem/1000000);
		}
		return -1;
	}

	// 设备可用内存
	public static int getMemoryAvailable(){
		Activity a = Cocos2dxHelper.getActivity();
		if ( a != null) {
			ActivityManager manager = (ActivityManager) a.getSystemService(Context.ACTIVITY_SERVICE);
			ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
			manager.getMemoryInfo(mi);
			return (int)(mi.availMem/1000000);
		}
		return -1;
	}

    // 获取CPU名字
	public static String getCPUName() {
		String result = "N/A";
		try {
			FileReader fileReader = new FileReader("proc/cpuinfo");
			BufferedReader bf = new BufferedReader(fileReader);
			String buf = bf.readLine();

			while (buf != null) {
				if (buf.contains("Hardware")) {
					result = buf.split(":")[1];
					break;
				}
				buf = bf.readLine();
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		return result;
	}

	// CPU核心数
	public static int getCPUCoreCount() {
		return Runtime.getRuntime().availableProcessors();
	}

	// 实时获取CPU当前频率（单位KHZ）
	public static String getCurCPUFreq() {
		String result = "";
		int coreCount = getCPUCoreCount();
		for (int i = 0; i < coreCount; i++){
			try {
				FileReader fr = new FileReader("/sys/devices/system/cpu/cpu" + String.valueOf(i) + "/cpufreq/scaling_cur_freq");
				BufferedReader br = new BufferedReader(fr);
				String text = br.readLine();
				result = result + text.trim() + (i == coreCount - 1 ? "" : ", ");
			} catch (FileNotFoundException e) {
				e.printStackTrace();
				result = result + "N/A";
			} catch (IOException e) {
				e.printStackTrace();
				result = result + "N/A";
			}
		}

		return result;
	}

	// 获取CPU最大频率（单位KHZ）
	// "/system/bin/cat" 命令行
	// "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq" 存储最大频率的文件的路径
	public static String getMaxCPUFreq() {
		String multiResult = "";
		ProcessBuilder cmd;

		int coreCount = getCPUCoreCount();
		for (int i = 0; i < coreCount; i++) {
			String result = "";
			try {
				String[] args = {"/system/bin/cat",
						"/sys/devices/system/cpu/cpu" + String.valueOf(i) + "/cpufreq/cpuinfo_max_freq"};

				cmd = new ProcessBuilder(args);
				Process process = cmd.start();
				InputStream in = process.getInputStream();
				byte[] re = new byte[24];
				while (in.read(re) != -1) {
					result = result + new String(re);
				}
				in.close();
				multiResult = multiResult + result.trim() + (i == coreCount - 1 ? "" : ", ");
			} catch (FileNotFoundException e) {
				e.printStackTrace();
				multiResult = multiResult + "N/A";
			} catch (IOException ex) {
				ex.printStackTrace();
				multiResult = multiResult + "N/A";
			}
		}

		return multiResult;
	}

	// 获取CPU最小频率（单位KHZ）
	public static String getMinCPUFreq() {
		String multiResult = "";
		ProcessBuilder cmd;

		int coreCount = getCPUCoreCount();
		for (int i = 0; i < coreCount; i++) {
			String result = "";
			try {
				String[] args = {"/system/bin/cat",
						"/sys/devices/system/cpu/cpu" + String.valueOf(i) + "/cpufreq/cpuinfo_min_freq"};

				cmd = new ProcessBuilder(args);
				Process process = cmd.start();
				InputStream in = process.getInputStream();
				byte[] re = new byte[24];
				while (in.read(re) != -1) {
					result = result + new String(re);
				}
				in.close();
				multiResult = multiResult + result.trim() + (i == coreCount - 1 ? "" : ", ");
			} catch (FileNotFoundException e) {
				e.printStackTrace();
				multiResult = multiResult + "N/A";
			} catch (IOException ex) {
				ex.printStackTrace();
				multiResult = multiResult + "N/A";
			}
		}

		return multiResult;
	}

	//cpu温度
	public static String getCPUTemInfo() {
		String result = "N/A";
		try {
			FileReader fr = new FileReader("/sys/class/thermal/thermal_zone1/temp");
			BufferedReader br = new BufferedReader(fr);
			String text = br.readLine();
			result = text.trim();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		return result;
	}

	//	查看CPU核动态调频的Governor
	public static String getCPUGovernor(){
		String result = "";
		int coreCount = getCPUCoreCount();
		for (int i = 0; i < coreCount; i++) {
			try {
				FileReader fr = new FileReader("/sys/devices/system/cpu/cpu" + String.valueOf(i) + "/cpufreq/scaling_governor");
				BufferedReader br = new BufferedReader(fr);
				String text = br.readLine();
				result = result + text.trim() + (i == coreCount - 1 ? "" : ", ");
			} catch (FileNotFoundException e) {
				e.printStackTrace();
				result = result + "N/A";
			} catch (IOException e) {
				e.printStackTrace();
				result = result + "N/A";
			}
		}

		return result;
	}

	// 电池状态
	public static String getBatteryInfo(){
		String result = "";

		IntentFilter ifilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
		Intent batteryStatus = Cocos2dxHelper.getActivity().registerReceiver(null, ifilter);
		// 电池状态 BATTERY_STATUS_ 相关
		int status = batteryStatus.getIntExtra(BatteryManager.EXTRA_STATUS, -1);
		// 充电方式 BATTERY_PLUGGED_ 相关
		int chargePlug = batteryStatus.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
		// 电池健康状态 BATTERY_HEALTH_ 相关
		int health = batteryStatus.getIntExtra(BatteryManager.EXTRA_HEALTH, -1);
		// 电池是否存在
		boolean present = batteryStatus.getBooleanExtra(BatteryManager.EXTRA_PRESENT, false);
		// 电池电量
		int level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
		// 电池总电量
		int scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
		// 电池温度
		int temp = batteryStatus.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1);
		// 电池技术
		String tech = batteryStatus.getStringExtra(BatteryManager.EXTRA_TECHNOLOGY);

		// status:5, plug:2, health:2, present:true, level:100, scale:100, temp:290, tech:Li-poly
		result = result + "status:" + String.valueOf(status) + ", ";
		result = result + "plug:" + String.valueOf(chargePlug) + ", ";
		result = result + "health:" + String.valueOf(health) + ", ";
		result = result + "present:" + String.valueOf(present) + ", ";
		result = result + "level:" + String.valueOf(level) + ", ";
		result = result + "scale:" + String.valueOf(scale) + ", ";
		result = result + "temp:" + String.valueOf(temp) + ", ";
		result = result + "tech:" + tech;

		return result;
	}

	// 电池容量
	public static String getBatteryCapacity(){
		String result = "N/A";
		Object mPowerProfile;
		double batteryCapacity = 0;
		final String POWER_PROFILE_CLASS = "com.android.internal.os.PowerProfile";
		try {
			mPowerProfile = Class.forName(POWER_PROFILE_CLASS)
					.getConstructor(Context.class)
					.newInstance(Cocos2dxHelper.getActivity());

			batteryCapacity = (double) Class
					.forName(POWER_PROFILE_CLASS)
					.getMethod("getBatteryCapacity")
					.invoke(mPowerProfile);

		} catch (Exception e) {
			e.printStackTrace();
		}

		result = String.valueOf(batteryCapacity);

		return result;
	}

	// 屏幕刷新率
	public static String getScreenRefreshRate(){
		Display display = Cocos2dxHelper.getActivity().getWindowManager().getDefaultDisplay();
		return String.valueOf(Math.round(display.getRefreshRate()));
	}

	// 更新Android检测CPU帧率的线程
	public static int startAndroidDetectCPUThread(){
		Cocos2dxHelper.getActivity().runOnUiThread(new Runnable() {
			@Override
			public void run() {
				((Cocos2dxActivity) Cocos2dxHelper.getActivity()).startDetectCPUThread();
			}
		});

		return 1;
	}

	// 停止ANR监听工具
	public static int stopWatchANR(){
		((Cocos2dxActivity)Cocos2dxHelper.getActivity()).stopANRMonitor();
		Cocos2dxHelper.stopWatchANR();
		return 1;
	}

	// 打印一次Raster消息数据
	public static int printRasterMessage(){
		((Cocos2dxActivity)Cocos2dxHelper.getActivity()).printRasterMessage();
		return 1;
	}

	// 设置一帧处理多少GLThread消息
	public static void setGLRunnableCountPerFrame(int count){
		Cocos2dxHelper.setGLRunnableCountPerFrame(count);
	}

	// 显示Toast 【sunyungao 23/09/06】
	public static void showToast(String content){
		Cocos2dxHelper.showToast(content);
	}

	// 获得CPU的ABI，用以确认是否为64位or32位【sunyungao 23/12/08】
	public static String getCPUABI(){
		String cpuABI;
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP){
			String[] cpuABIs = Build.SUPPORTED_ABIS; // arm64-v8,armewabi-v7a,armeabi
			cpuABI = cpuABIs[0];
		}else {
			cpuABI = Build.CPU_ABI;
		}

		return cpuABI;
	}

	// Google 意见征求 管理解决方案
	public static void confirmGoogleCMP(){
		Cocos2dxHelper.getActivity().runOnUiThread(new Runnable() {
			@Override
			public void run() {
				((Cocos2dxActivity)Cocos2dxHelper.getActivity()).googleCMPStart();
			}
		});
	}

	// Google 意见征求 管理解决方案,用户更改按钮调用
	public static void googleCMPOptionsCall(){
		Cocos2dxHelper.getActivity().runOnUiThread(new Runnable() {
			@Override
			public void run() {
				((Cocos2dxActivity)Cocos2dxHelper.getActivity()).googleCMPOptions();
			}
		});
	}

	public static boolean googleCMPIsPrivacyOptionsRequired(){
		return ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).isGoogleCMPPrivacyOptionsRequired();
	}

	//
	public static boolean googleCMPCanRequestAds(){
		return ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).googleCMPCanRequestAds();
	}

	public static int getLaunchPath(){
		return ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getLaunchPath();
	}

	// JVM 内存
	// {"maxMemory":"256.00 mB","totalMemory":"54.63 mB","freeMemory":"0.00 mB","useMemory":"54.63 mB",
	// "actualFreeMemory":"201.37 mB","use_memory_rate":"21.34%"}
	public static String getJVMMemory(){
		JSONObject json1 = MemoryMonitor.statisticsJVMMemory();
		return json1.toString();
	}

	// 当前进程中native层申请的堆内存，会随着时间而变化，加大或者减少
	// {"TotalNative":"304.39 mB","useNative":"178.12 mB","freeNative":"85.60 mB","free_memory_rate":"28.12%"}
	public static String getNativeMemory(){
		JSONObject json1 = MemoryMonitor.statisticsNativeMemory();
		return json1.toString();
	}

	// 计算进程中内存状况和线程状况,/proc/self/status 命令获取的数据
	// {"FDSize":"2048","VmPeak":"41579444 kB","VmSize":"36076844 kB","VmRSS":"909928 kB","VmData":"1630688 kB","Threads":"226"}
	public static String getProcessMemory(){
		JSONObject json1 = MemoryMonitor.statisticsProcessMemory();
		return json1.toString();
	}

	// 统计系统内存
	// {"MemTotal":"3660 mB","MemFree":"213 mB","MemAvailable":"432 mB","free_memory_rate":"11.80%"}
	public static String getSystemMemory(){
		JSONObject json1 = MemoryMonitor.statisticsSystemMemory();
		return json1.toString();
	}

	// 查看当前FD数量
	// 1304
	public static String getFDCount(){
		int fdCount = MemoryMonitor.listFd();
		return String.valueOf(fdCount);
	}

	public static int getIntFDCount(){
		return MemoryMonitor.listFd();
	}

	// 内存总结，与profiler里内存相同
	// {"javaHeap":"38.77mB","nativeHeap":"125.30mB","code":"36.22mB","stack":"2.12mB","graphics":"525.74mB",
	// "privateOther":"96.54mB","system":"153.00mB","swap":"197.76mB","total":"1175.45mB"}
	public static String getAPPSummaryMemory(){
		JSONObject json1 = MemoryMonitor.statisticsAPPSummaryMemory();
		return json1.toString();
	}

	// 获取设备sd卡存储
	// {"freeStorage":"5990mB","totalStorage":"50853mB"}
	public static String getSDStorage(){
		JSONObject json1 = MemoryMonitor.statisticsSDStorage();
		return json1.toString();
	}

	// 设备总内存(totalMem 与 getSystemMemory 函数的 MemTotal一致)
	// {"totalMem":"3660mB","availMem":"692mB"}
	public static String getDeviceMemory(){
		JSONObject json1 = MemoryMonitor.statisticsDeviceMemory();
		return json1.toString();
	}

	public static String getDeviceMemoryDetail(){
		return MemoryMonitor.statisticsDeviceMemoryDetail();
	}

	// 读取发生crash 或者 ANR 的信息
	public static String readCrashOrANRString(){
		return MemoryMonitor.readCrashOrANRData();
	}

	// 清空存储 crash 或者 ANR 的文件的数据
	public static void clearCrashOrANRString(){
		MemoryMonitor.clearCrashOrANRData();
	}

	// 进行一次数据存储，在crash 或者 ANR 时收集此数据到服务器
	public static void collectCrashMemoryInfo(int maxCount, String param){
		MemoryMonitor.collectMemoryInfo(maxCount, param);
	}

	// 读取发生crash 或者 ANR 的信息
	public static String readCrashMemoryInfo(){
		return MemoryMonitor.readMemoryInfo();
	}

	// 清空存储 crash 或者 ANR 的文件的数据
	public static void clearCrashMemoryInfo(){
		MemoryMonitor.clearMemoryInfo();
	}

	// 获取手机内某应用是否安装
	public static boolean isAppInstalled(final String packageName)
	{
		final Activity activity = Cocos2dxHelper.getActivity();
		if (activity != null) {
			try
			{
				activity.getPackageManager().getPackageInfo(packageName,0);
				return true;
			}
			catch (PackageManager.NameNotFoundException ex)
			{
				return false;
			}
		}
		return false;
	}

	// 获取手机内某应用的版本号
	public static String getInstalledAppVersion(String packageName){
		String version = "0";
		final Activity activity = Cocos2dxHelper.getActivity();
		if (activity != null) {
			try
			{
				PackageInfo pkgInfo = activity.getPackageManager().getPackageInfo(packageName,0);
				if (pkgInfo != null)
				{
					version = pkgInfo.versionName;
				}
				return version;
			}
			catch (PackageManager.NameNotFoundException ex)
			{
				Log.d("getInstalledAppVersion:",  "2");
				return version;
			}
		}
		return version;
	}
}

