package org.cocos2dx.bole.notification;


import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

public class AlarmReceiver extends BroadcastReceiver{

	@Override
	public void onReceive(Context context, Intent intent) {
		// TODO Auto-generated method stub
//	      Intent service = new Intent(context, AlarmService.class);
//	      service.putExtras(intent.getExtras());
////	      context.startService(service);
//		try {
//			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//				context.startForegroundService(service);
//			} else {
//				context.startService(service);
//			}
//		} catch (Exception e) {
//		}
		if (intent != null) {
			String message = intent.getStringExtra("message");
			String title = intent.getStringExtra("title");
			if (title != null && title.equals("1")){
				BoleNotification.showNotificationCustom(context.getApplicationContext(), title, message,"",null);
			}else
			{
				BoleNotification.showNotification(context.getApplicationContext(), title, message,"",null);
			}

		}
	}
}
