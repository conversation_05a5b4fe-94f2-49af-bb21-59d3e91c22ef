package org.cocos2dx.bole.notification;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.LinkedList;
import java.util.Random;

import org.cocos2dx.lib.Cocos2dxHelper;
import org.cocos2dx.lua.AppActivity;
import org.cocos2dx.lua.BoleApplication;


import android.app.Activity;
import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.NotificationChannel;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import androidx.core.app.NotificationCompat;

import android.text.Html;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.RemoteViews;
import android.widget.TextView;

import com.frontier.sdkbase.SDKManager;
import com.grandegames.slots.dafu.casino.R;

public class BoleNotification {
	public static final String NOTIFICATION_SERVICE = "notification";
	public static final int NOTIFICATION_ID = 1;
	static String tag = "BoleNotification";
	private static final String TAG = "NotificationTest";
	public static final String  IMGTAG = "#IMG";

	public static void createNotificationChannel(Context context) {

	}
	/**
	 * 发送只有文字内容的推送
	 * @param context
	 * @param title		推送标题
	 * @param content	推送内容
	 * @param extraData	Intent
	 * @return
	 */
	public static int showCustomNotificationWithOnlyText(Context context, String title, String content, Intent extraData)
	{
		Handler handler = new Handler(context.getMainLooper());
		handler.post(new Runnable() {
			@Override
			public void run() {
				Log.d(TAG, "UJH_ADD_LOG 1");
				NotificationManager notificationManager = (NotificationManager)context.getSystemService(Context.NOTIFICATION_SERVICE);
				PendingIntent pendingIntent;
				if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
					pendingIntent = PendingIntent.getActivity(context, 0, extraData, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

				}else {
					pendingIntent = PendingIntent.getActivity(context, 0, extraData, PendingIntent.FLAG_UPDATE_CURRENT);
				}
				NotificationCompat.Builder builder = new NotificationCompat.Builder(context, SDKManager.getNotificationSoundConfig(context));
				Log.d(TAG, "UJH_ADD_LOG 2 title="+title+", content="+content);
				builder.setSmallIcon(R.drawable.notification_icon)
						.setAutoCancel(true)
						.setContentIntent(pendingIntent)
						.setWhen(System.currentTimeMillis())
						.setContentTitle(title)
						.setContentText(content)
						.setPriority(NotificationCompat.PRIORITY_HIGH);
				notificationManager.notify(NOTIFICATION_ID, builder.build());
				Log.d(TAG, "UJH_ADD_LOG 3");
			}
		});

		return 0;
	}

	/**
	 * 发送带有文字和一张图片的推送
	 * @param context
	 * @param title		推送标题
	 * @param content	推送内容
	 * @param picUrl	图片url
	 * @param extraData	Intent
	 * @return
	 */
	public static int showCustomNotificationWithOnePicture(Context context, String title, String content, String picUrl, Intent extraData)
	{
		Log.d(TAG, "UJH_ADD_LOG 4");
		Bitmap pic = getBitmapfromUrl(picUrl);
		Log.d(TAG, "UJH_ADD_LOG 5");
		if(pic != null) {
			Log.d(TAG, "UJH_ADD_LOG 6");
			return showCustomNotificationWithOnePicture(context, title, content, pic, extraData);
		} else {
			Log.d(TAG, "UJH_ADD_LOG 7");
			return showCustomNotificationWithOnlyText(context, title, content, extraData);
		}
	}

	/**
	 * 发送带有文字和一张图片的推送
	 * @param context
	 * @param title		推送标题
	 * @param content	推送内容
	 * @param pic		Bitmap图片
	 * @param extraData	Intent
	 * @return
	 */
	public static int showCustomNotificationWithOnePicture(Context context, String title, String content, Bitmap pic, Intent extraData)
	{
		Handler handler = new Handler(context.getMainLooper());
		handler.post(new Runnable() {
			@Override
			public void run() {
				Log.d(TAG, "UJH_ADD_LOG 8");
				createNotificationChannel(context);
				PendingIntent pendingIntent;
				if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
					pendingIntent = PendingIntent.getActivity(context, 0, extraData, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

				}else {
					pendingIntent = PendingIntent.getActivity(context, 0, extraData, PendingIntent.FLAG_UPDATE_CURRENT);
				}
				NotificationCompat.Builder builder = new NotificationCompat.Builder(context, SDKManager.getNotificationSoundConfig(context));
				builder.setSmallIcon(R.drawable.notification_icon)
						.setAutoCancel(true)
						.setContentIntent(pendingIntent)
						.setContentTitle(title)
						.setContentText(content)
						.setPriority(NotificationCompat.PRIORITY_HIGH);
				Log.d(TAG, "UJH_ADD_LOG 9");
				if(pic != null) {
					builder.setLargeIcon(BitmapFactory.decodeResource(context.getResources(), R.drawable.notification_icon))
							.setStyle(new NotificationCompat.BigPictureStyle()
									.bigPicture(pic));
					NotificationManager notificationManager = (NotificationManager)context.getSystemService(Context.NOTIFICATION_SERVICE);
					notificationManager.notify(NOTIFICATION_ID, builder.build());
				} else {
					showCustomNotificationWithOnlyText(context, title, content, extraData);
				}
			}
		});

		return 0;
	}

	/**
	 * 发送带有两张图片的推送
	 * 因为需要下载图片，如果有一张图片下载失败，则按照发送一张图片的推送发出
	 * 如果两张图片都下载失败，则按照只有文字的推送发出
	 * @param context
	 * @param title		推送标题
	 * @param content	推送内容
	 * @param picUrl1	小图片url
	 * @param picUrl2	大图片url
	 * @param extraData	Intent
	 * @return
	 */
	public static int showCustomNotificationWithTwoPictures(Context context, String title, String content, String picUrl1, String picUrl2, Intent extraData)
	{
		Log.d(TAG, "UJH_ADD_LOG 15");
		Bitmap pic1 = getBitmapfromUrl(picUrl1);
		Log.d(TAG, "UJH_ADD_LOG 16");
		Bitmap pic2 = getBitmapfromUrl(picUrl2);
		Log.d(TAG, "UJH_ADD_LOG 17");
		if(pic1 != null && pic2 != null) {
			Log.d(TAG, "UJH_ADD_LOG 18");
			return showCustomNotificationWithTwoPictures(context, title, content, pic1, pic2, extraData);
		} else if(pic1 != null && pic2 == null) {
			Log.d(TAG, "UJH_ADD_LOG 19");
			return showCustomNotificationWithOnePicture(context, title, content, pic1, extraData);
		} else if(pic1 == null && pic2 != null) {
			Log.d(TAG, "UJH_ADD_LOG 20");
			return showCustomNotificationWithOnePicture(context, title, content, pic2, extraData);
		} else if(pic1 == null && pic2 == null) {
			Log.d(TAG, "UJH_ADD_LOG 21");
			return showCustomNotificationWithOnlyText(context, title, content, extraData);
		}

		return 0;
	}

	/**
	 * 发送带有两张图片的推送
	 * @param context
	 * @param title		推送标题
	 * @param content	推送内容
	 * @param pic1		小图片Bitmap
	 * @param pic2		大图片Bitmap
	 * @param extraData	Intent
	 * @return
	 */
	public static int showCustomNotificationWithTwoPictures(Context context, String title, String content, Bitmap pic1, Bitmap pic2, Intent extraData)
	{
		Handler handler = new Handler(context.getMainLooper());
		handler.post(new Runnable() {
			@Override
			public void run() {
				Log.d(TAG, "UJH_ADD_LOG 22");
				createNotificationChannel(context);
				NotificationManager notificationManager = (NotificationManager)context.getSystemService(Context.NOTIFICATION_SERVICE);
				PendingIntent pendingIntent;
				if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
					pendingIntent = PendingIntent.getActivity(context, 0, extraData, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

				}else {
					pendingIntent = PendingIntent.getActivity(context, 0, extraData, PendingIntent.FLAG_UPDATE_CURRENT);
				}

				Log.d(TAG, "UJH_ADD_LOG 23");
				RemoteViews notification_small = new RemoteViews(context.getPackageName(), R.layout.two_pics_notification);
				notification_small.setImageViewBitmap(R.id.preview, pic1);
				notification_small.setImageViewResource(R.id.notification_icon, R.drawable.notification_icon);

				Log.d(TAG, "UJH_ADD_LOG 24");
				RemoteViews notification_big = new RemoteViews(context.getPackageName(), R.layout.two_pics_notification);
				notification_big.setImageViewBitmap(R.id.preview, pic2);
				notification_big.setImageViewResource(R.id.notification_icon, R.drawable.notification_icon);

				Log.d(TAG, "UJH_ADD_LOG 25");
				NotificationCompat.Builder builder = new NotificationCompat.Builder(context, SDKManager.getNotificationSoundConfig(context));
				builder.setSmallIcon(R.drawable.notification_icon)
						.setAutoCancel(true)
						.setContentIntent(pendingIntent)
						.setSound(Uri.parse("android.resource://" + context.getPackageName() + "/raw/n2"))
						.setCustomContentView(notification_small)
						.setCustomBigContentView(notification_big)
						.setContentIntent(pendingIntent)
						.setContentTitle(title)
						.setContentText(content)
						.setPriority(NotificationCompat.PRIORITY_HIGH);
				Log.d(TAG, "UJH_ADD_LOG 26");
				notificationManager.notify(NOTIFICATION_ID, builder.build());
				Log.d(TAG, "UJH_ADD_LOG 27");
			}
		});
		return 0;
	}

	public static String showNotification(Context context, String title, String desc,String bigPicturelink,Bundle pushData){
		if (title == null)
			title = "";
		Log.i(tag,title);
		if (desc == null)
			desc = "";
		Log.i(tag,desc);
		if (bigPicturelink == null)
			bigPicturelink = "";
		Log.i(tag,bigPicturelink);

		//前台的时候，忽略通知。
		if (BoleApplication.isActivityVisible()){
			Log.i(tag,"front ignore notification");
			return "failed";
		}

		Intent intent = new Intent(context,
				AppActivity.class).setAction(Intent.ACTION_DEFAULT).putExtra("push", pushData);
		PendingIntent pendingIntent;
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
			pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);

		}else {
			pendingIntent = PendingIntent.getActivity(context, 0, intent, 0);
		}
		try {

			ApplicationInfo appInfo =  context.getApplicationContext().getPackageManager().getApplicationInfo(context.getPackageName(),
					PackageManager.GET_META_DATA);
			String default_notification_channel_id = appInfo.metaData.getString("com.google.firebase.messaging.default_notification_channel_id");
			int ic_stat_ic_notification = appInfo.metaData.getInt("com.google.firebase.messaging.default_notification_icon");
			int color = appInfo.metaData.getInt("com.google.firebase.messaging.default_notification_color");
			String channelId = default_notification_channel_id;

			NotificationCompat.Builder notificationBuilder = null;
			if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
				// >= 8.0
				NotificationChannel channel = new NotificationChannel("channelId",
						"NOTIFICATION", NotificationManager.IMPORTANCE_DEFAULT);
				channel.enableLights(true); //设置开启指示灯，如果设备有的话
				channel.setLightColor(Color.RED); //设置指示灯颜色
				channel.setShowBadge(true); //设置是否显示角标
				channel.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE); //设置是否应在锁定屏幕上显示此频道的通知
				channel.setDescription("NOTIFICATION"); //设置渠道描述
				channel.setVibrationPattern(new long[]{100,200,300,100,200,300}); //设置震动频率
				channel.setBypassDnd(true); //设置是否绕过免打扰模式

				NotificationManager notificationManager = (NotificationManager) context.getSystemService(
						Context.NOTIFICATION_SERVICE);
				notificationManager.createNotificationChannel(channel);

				//createNotificationChannelGroups();
				//setNotificationChannelGroups(channel);
				notificationBuilder = new NotificationCompat.Builder(context, "channelId");
				//notificationBuilder.setBadgeIconType(BADGE_ICON_SMALL);//设置显示角标的样式
				//notificationBuilder.setNumber(3);//设置显示角标的数量
				//notificationBuilder.setTimeoutAfter(5000);//设置通知被创建多长时间之后自动取消通知栏的通知。
			}else{
				//notificationBuilder = new NotificationCompat.Builder(this);
				notificationBuilder = new NotificationCompat.Builder(context);
			}

//			NotificationCompat.Builder notificationBuilder =
//					new NotificationCompat.Builder(context);

			notificationBuilder.setContentTitle(title)
					.setContentText(desc)
					.setAutoCancel(true)
					.setWhen(System.currentTimeMillis())
//					.setSmallIcon(context.getResources().getIdentifier("icon", "drawable", context.getPackageName()))
					.setSound(Uri.parse("android.resource://" + context.getPackageName() + "/raw/n2"))
					.setColor(color)
					.setContentIntent(pendingIntent)
					.setSmallIcon(ic_stat_ic_notification);

			//包含img. 重新设置title body。local bigpicture - title ->(xxx#IMG)   remote ->link is not empty.
			if (title.toUpperCase().contains(IMGTAG) || !bigPicturelink.equals("")){
				//没有链接读取本地
				Bitmap bitmap = null;
				String content  =  desc;
				if (!bigPicturelink.equals("")){
					bitmap = getBitmapfromUrl(bigPicturelink);
				}
				if (bitmap == null){
					int bigpicureid = appInfo.metaData.getInt("com.google.firebase.messaging.defaultbigpicureid");
					bitmap = BitmapFactory.decodeResource(context.getResources(),bigpicureid);
				}

				if (bitmap == null){
					Log.i(tag,"bitmap is null");
				}else{
					Log.i(tag,"bitmap is OK");
				}
				String leftTitle  =  getRealContent(title,true);
				notificationBuilder.setContentTitle(leftTitle);
				notificationBuilder.setContentText(desc);

				notificationBuilder.setLargeIcon(bitmap)
						.setStyle(new NotificationCompat.BigPictureStyle().bigPicture(bitmap));

			}else{
				notificationBuilder.setStyle(new NotificationCompat.BigTextStyle().bigText(desc));
			}

			NotificationManager notificationManager = (NotificationManager) context.getSystemService(
					Context.NOTIFICATION_SERVICE);
			notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build());

		} catch (Exception e) {
			e.printStackTrace();
		}


		return "success";
	}


	public static String showNotificationCustom(Context context, String title, String desc,String bigPicturelink,Bundle pushData){
		if (title == null)
			title = "";
		Log.i(tag,title);
		if (desc == null)
			desc = "";
		Log.i(tag,desc);
		if (bigPicturelink == null)
			bigPicturelink = "";
		Log.i(tag,bigPicturelink);

		//前台的时候，忽略通知。
		if (BoleApplication.isActivityVisible()){
			Log.i(tag,"front ignore notification");
			return "failed";
		}

		Intent intent = new Intent(context,
				AppActivity.class).setAction(Intent.ACTION_DEFAULT).putExtra("push", pushData);
		PendingIntent pendingIntent;
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
			pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);

		}else {
			pendingIntent = PendingIntent.getActivity(context, 0, intent, 0);
		}

		try {

			ApplicationInfo appInfo =  context.getApplicationContext().getPackageManager().getApplicationInfo(context.getPackageName(),
					PackageManager.GET_META_DATA);
			String default_notification_channel_id = appInfo.metaData.getString("com.google.firebase.messaging.default_notification_channel_id");
			int ic_stat_ic_notification = appInfo.metaData.getInt("com.google.firebase.messaging.notification_icon");
			int color = appInfo.metaData.getInt("com.google.firebase.messaging.default_notification_color");
			int defaultImgId = appInfo.metaData.getInt("com.google.firebase.messaging.notification_background");
			NotificationCompat.Builder builder = new NotificationCompat.Builder(context)
					.setSmallIcon(ic_stat_ic_notification)
					.setAutoCancel(true)
					.setContentIntent(pendingIntent);
			builder.setWhen(System.currentTimeMillis());
			builder.setSound(Uri.parse("android.resource://" + context.getPackageName() + "/raw/n2"));

			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
				// build a complex notification, with buttons and such
				Log.i(tag,"Show custom notification");

				int layoutId = appInfo.metaData.getInt("com.google.firebase.messaging.custom_notification");
				int imageViewid = appInfo.metaData.getInt("com.google.firebase.messaging.backgroundBg");
				int iconViewid = appInfo.metaData.getInt("com.google.firebase.messaging.notificaionicon");
				int Textid =  appInfo.metaData.getInt("com.google.firebase.messaging.text1");
				int Text2id =  appInfo.metaData.getInt("com.google.firebase.messaging.text2");
				int inconId = appInfo.metaData.getInt("com.google.firebase.messaging.notification_icon");

				RemoteViews notificationView = new RemoteViews(
						context.getPackageName(),
						layoutId
				);

				Log.i(tag,"defaultImgId is " + String.valueOf(defaultImgId));
				Log.i(tag,"imageViewid is " + String.valueOf(imageViewid));
				Log.i(tag,"iconViewid is " + String.valueOf(iconViewid));
				Log.i(tag,"ic_stat_ic_notification is " + String.valueOf(ic_stat_ic_notification));
//				notificationView.setImageViewResource(iconViewid,ic_stat_ic_notification);
				notificationView.setImageViewResource(imageViewid,defaultImgId);
				notificationView.setImageViewResource(iconViewid,inconId);
				notificationView.setTextViewText(Textid,title);
				notificationView.setTextViewText(Text2id,desc);
				//适配背景颜色,采用反射方法，进行设置颜色。bing。由于img作为底图，不需要。
				boolean isDarkTheme =  isDarkNotificationTheme(context);
				Log.i(tag,"theme color is " + String.valueOf(isDarkTheme));
//				notificationView.setInt(Textid, "setTextColor", isDarkTheme?Color.WHITE:Color.BLACK);
//				notificationView.setInt(imageViewid, "setBackgroundColor", isDarkTheme?Color.BLACK:Color.WHITE);

				builder = builder.setContent(notificationView);

			} else {
				// Build a simpler notification, without buttons
				//
				builder = builder.setContentTitle(title)
						.setContentText(desc)
						.setSmallIcon(ic_stat_ic_notification);
				builder.setContentTitle(title);
				builder.setContentText(desc);
			}

			NotificationManager notificationManager = (NotificationManager) context.getSystemService(
					Context.NOTIFICATION_SERVICE);
			notificationManager.notify(NOTIFICATION_ID, builder.build());

		} catch (Exception e) {
			e.printStackTrace();
		}


		return "success";
	}


	public static void clearNotifications() {
		Log.i("clearNotifications","1");
		try {
			Activity a = Cocos2dxHelper.getActivity();
			AlarmManager alarmMgr = (AlarmManager)a.getSystemService(Context.ALARM_SERVICE);
			Intent intent = new Intent(a, AlarmReceiver.class);
			PendingIntent alarmIntent;
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
				alarmIntent = PendingIntent.getBroadcast(a, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

			}else {
				alarmIntent = PendingIntent.getBroadcast(a, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
			}
			alarmMgr.cancel(alarmIntent);
		}
		catch (RuntimeException e){
			e.printStackTrace();
		}

	}


	/*
	 *To get a Bitmap image from the URL received
	 * */
	public static Bitmap getBitmapfromUrl(String imageUrl) {
		try {
			URL url = new URL(imageUrl);
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setDoInput(true);
			connection.connect();
			InputStream input = connection.getInputStream();
			Bitmap bitmap = BitmapFactory.decodeStream(input);
			return bitmap;

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;

		}
	}

	//bLeft true->get left part false get right part
	public static String getRealContent(final String content , Boolean bLeft)
	{
		String ret= "";
		if (content == null || content.equals("")){
			return ret;
		}
		final String SpTag = "#";
		if (content.indexOf(SpTag) < 0){
			ret = content;
			return  ret;
		}
//        String [] spStrs = content.split(SpTag);
		int pos =  content.indexOf(SpTag);
		if (bLeft){
			ret = content.substring(0,pos);
		}else{
			ret = content.substring(pos+1,content.length());
		}
		return  ret;
	}

	public static String addNotification(final String title, final String content,int delaytime)
	{
		Log.i("addNotification title",title);
		Log.i("addNotification content",content);
		Log.i("addNotification time", String.valueOf(delaytime));
		try {
			AlarmManager alarmMgr;
			PendingIntent alarmIntent;
			Activity activity = Cocos2dxHelper.getActivity();
			Context context = activity.getApplicationContext();
			alarmMgr = (AlarmManager)context.getSystemService(Context.ALARM_SERVICE);
			Intent intent = new Intent(context, AlarmReceiver.class);
			intent.putExtra ("message", content);
			intent.putExtra ("title", title);
			Random rand = new Random();
			// nextInt is normally exclusive of the top value,
			// so add 1 to make it inclusive
			int randomNum = Math.abs(rand.nextInt());
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
				alarmIntent = PendingIntent.getBroadcast(context, randomNum, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

			}else {
				alarmIntent = PendingIntent.getBroadcast(context, randomNum, intent, PendingIntent.FLAG_UPDATE_CURRENT);
			}

			alarmMgr.set(AlarmManager.ELAPSED_REALTIME,
					SystemClock.elapsedRealtime() +delaytime * 1000,
					alarmIntent);

			return "success  "+title+content+String.valueOf(delaytime);
		} catch (Exception e) {
			return "error";
		}
	}


	public static boolean isDarkNotificationTheme(Context context) {
		return !isSimilarColor(Color.BLACK, getNotificationColor(context));
	}

	/**
	 * 获取通知栏颜色
	 * @param context
	 * @return
	 */
	public static int getNotificationColor(Context context) {
		NotificationCompat.Builder builder=new NotificationCompat.Builder(context);
		Notification notification=builder.build();
		int layoutId=notification.contentView.getLayoutId();
		ViewGroup viewGroup= (ViewGroup) LayoutInflater.from(context).inflate(layoutId, null, false);
		if (viewGroup.findViewById(android.R.id.title)!=null) {
			return ((TextView) viewGroup.findViewById(android.R.id.title)).getCurrentTextColor();
		}
		return findColor(viewGroup);
	}

	private static boolean isSimilarColor(int baseColor, int color) {
		int simpleBaseColor=baseColor|0xff000000;
		int simpleColor=color|0xff000000;
		int baseRed=Color.red(simpleBaseColor)-Color.red(simpleColor);
		int baseGreen=Color.green(simpleBaseColor)-Color.green(simpleColor);
		int baseBlue=Color.blue(simpleBaseColor)-Color.blue(simpleColor);
		double value=Math.sqrt(baseRed*baseRed+baseGreen*baseGreen+baseBlue*baseBlue);
		if (value<180.0) {
			return true;
		}
		return false;
	}

	private static int findColor(ViewGroup viewGroupSource) {
		int color=Color.TRANSPARENT;
		LinkedList<ViewGroup> viewGroups=new LinkedList<>();
		viewGroups.add(viewGroupSource);
		while (viewGroups.size()>0) {
			ViewGroup viewGroup1=viewGroups.getFirst();
			for (int i = 0; i < viewGroup1.getChildCount(); i++) {
				if (viewGroup1.getChildAt(i) instanceof ViewGroup) {
					viewGroups.add((ViewGroup) viewGroup1.getChildAt(i));
				}
				else if (viewGroup1.getChildAt(i) instanceof TextView) {
					if (((TextView) viewGroup1.getChildAt(i)).getCurrentTextColor()!=-1) {
						color=((TextView) viewGroup1.getChildAt(i)).getCurrentTextColor();
					}
				}
			}
			viewGroups.remove(viewGroup1);
		}
		return color;
	}
}

