package org.cocos2dx.bole.notification;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import androidx.core.app.NotificationCompat;

import android.os.Build;

public class AlarmService extends Service {

	  public static  String TAG = "AlarmService";
	    @Override
	    public IBinder onBind(Intent arg0)
	    {
	       // TODO Auto-generated method stub
	        return null;
	    }
	 
	    @Override
	    public void onCreate() 
	    {
	       // TODO Auto-generated method stub  
	       super.onCreate();
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
				String CHANNEL_ID = "my_channel_01";
				NotificationChannel channel = new NotificationChannel(CHANNEL_ID,
						"background title",
						NotificationManager.IMPORTANCE_DEFAULT);

				((NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE)).createNotificationChannel(channel);

				Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
						.setContentTitle("")
						.setContentText("").build();

				startForeground(1, notification);
			}
			else {
				startForeground(1, new Notification());
			}
	    }
	 
	   @SuppressWarnings("static-access")
	   @Override
	   public void onStart(Intent intent, int startId)
	   {
	       if (intent != null) {
	    	   super.onStart(intent, startId);
		       String message = intent.getStringExtra("message");
		       String title = intent.getStringExtra("title");
			   if (title != null && title.equals("1")){
				   BoleNotification.showNotificationCustom(this.getApplicationContext(), title, message,"",null);
			   }else
			   {
				   BoleNotification.showNotification(this.getApplicationContext(), title, message,"",null);
			   }

	       }
	    }

//	    @Override
//		public int onStartCommand(Intent intent, int flags, int startId) {
//
//			Log.i(TAG, "onStartCommand");
//			if (intent != null) {
//				super.onStartCommand(intent, flags, startId);
//				String message = intent.getStringExtra("message");
//				String title = intent.getStringExtra("title");

//			}
//			return START_REDELIVER_INTENT;
//		}

}