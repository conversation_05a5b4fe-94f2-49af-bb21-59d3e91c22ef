package org.cocos2dx.bole.amazon.iap;

import android.app.Activity;
import android.util.Log;
import android.view.View;

import com.amazon.device.iap.PurchasingService;
import com.amazon.device.iap.model.RequestId;

import org.cocos2dx.bole.amazon.iap.SampleIapManager;
import org.cocos2dx.bole.amazon.iap.SamplePurchasingListener;
import org.cocos2dx.lib.Cocos2dxLuaJavaBridge;

public class AmazonIapManager {
	public static String TAG = "AmazonIapManager";
	public static SampleIapManager sampleIapManager;
	public static int luaCallback;
	 /**
     * Setup for IAP SDK called from onCreate. Sets up {@link SampleIapManager}
     * to handle InAppPurchasing logic and {@link SamplePurchasingListener} for
     * listening to IAP API callbacks
     */
    public static void setupIAPOnCreate(Activity a) {
        sampleIapManager = new SampleIapManager(a);
        sampleIapManager.activate();
        final SamplePurchasingListener purchasingListener = new SamplePurchasingListener(sampleIapManager);
        Log.d(TAG, "onCreate: registering PurchasingListener");
        PurchasingService.registerListener(a.getApplicationContext(), purchasingListener);
        Log.d(TAG, "IS_SANDBOX_MODE:" + PurchasingService.IS_SANDBOX_MODE);
    }
    
    public static void onResume() {
        sampleIapManager.activate();
        Log.d(TAG, "onResume: call getUserData");
        PurchasingService.getUserData();
    }

    public static String purchase(String sku, int luaFunc) {
        Log.d(TAG, "purchase   "+ sku + "  "+ luaFunc);
        luaCallback = luaFunc;
        RequestId requestId = PurchasingService.purchase(sku);
        return requestId.toString();
    }
    
    public static void getPurchaseUpdates(int luaFunc) {
        Log.d(TAG, "getPurchaseUpdates "+ luaFunc);
        luaCallback = luaFunc;
        PurchasingService.getPurchaseUpdates(false);
    }
    
//    public static void callbackLua(final String tipInfo,final int luaFunc){
//    	Log.d(TAG, "callbackLua   "+ tipInfo + "  "+ luaFunc);
//		Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaFunc, "success");
//		Cocos2dxLuaJavaBridge.releaseLuaFunction(luaFunc);
//	}

    public static void consume(String receiptId, String userId) {
    	sampleIapManager.handleVerifiedPurchase(receiptId, userId);
    }
}
