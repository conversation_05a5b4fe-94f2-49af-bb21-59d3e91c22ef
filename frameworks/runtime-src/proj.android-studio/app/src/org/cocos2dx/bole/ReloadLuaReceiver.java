
package org.cocos2dx.bole;

import java.lang.reflect.Method;

import org.cocos2dx.lib.Cocos2dxLuaJavaBridge;
import org.cocos2dx.plugin.PluginWrapper;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

public class ReloadLuaReceiver extends BroadcastReceiver {
	String TAG = "ReferrerReceiver";
	@Override
	public void onReceive(Context context, Intent intent) {
		// TODO Auto-generated method stub
		final String action = intent.getAction();
	 	Log.i(TAG,"get action is"+action+" data is:"+intent.getDataString());
        if (action != null && TextUtils.equals(action, "com.bolegames.test.Reload")) {
        	final String data = intent.getDataString();
        	if (PluginWrapper.getContext() != null)
        	PluginWrapper.runOnGLThread(new Runnable(){
				@Override
				public void run() {
					try {
					// TODO Auto-generated method stub
						Cocos2dxLuaJavaBridge.callLuaGlobalFunctionWithString("__G__ReloadLua__",data);
					} catch (Exception e) {
		            	Log.i(TAG, e.toString());
		                e.printStackTrace();
		            }
				}
        	}, "ReloadLuaReceiver_onReceive");
        }
	}
}

