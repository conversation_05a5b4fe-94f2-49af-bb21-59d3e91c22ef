package org.cocos2dx.bole.notification;


import java.util.Random;
import java.util.Timer;
import java.util.TimerTask;
import java.util.Vector;

import org.cocos2dx.lib.Cocos2dxHelper;
import org.cocos2dx.lua.AppActivity;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.preference.PreferenceManager;
import android.util.Log;

public class BootReceiver extends BroadcastReceiver {  
	final static String TITLE= "tile";  
	final static String MESSAGE= "message";
	final static String CHANCE_PERCENT = "chancePercent";
	final int NOTIFICATION_ID = 19870311;
	public static final String TAG = "bole";
    @Override  
    public void onReceive(Context context, Intent intent) {  
    	try{ 
	        if(intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED)){  
	        	final SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
	            String title = prefs.getString(TITLE, "");
	            String message = prefs.getString(MESSAGE, "");
	            int chancePercent = prefs.getInt(CHANCE_PERCENT, 0);
	            if (title.isEmpty() || message.isEmpty()) {
	                Log.i(TAG, "no notification.");
	                return;
	            }
	            if (Math.random() * 100 > chancePercent){
	            	Log.i(TAG, "chancePercent is"+chancePercent);
	            	return;
	            }
	            if (title != null && title.equals("1")){
					BoleNotification.showNotificationCustom(context, title, message,"",null);
				}else
				{
					BoleNotification.showNotification(context, title, message,"",null);
				}

	    	} 
		} catch (Exception e) {
			Log.w(TAG, "failed in show Boot Notification");
			}
    }
    
    public static String setBootNotification(final String title, final String content, int chancePercent){
    	try{
    		Activity a = Cocos2dxHelper.getActivity();
    		final SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(a);
    		SharedPreferences.Editor editor = prefs.edit();
            editor.putString(TITLE, title);
            editor.putString(MESSAGE, content);
            editor.putInt(CHANCE_PERCENT, chancePercent);
            editor.commit();
            return "success";
    	}catch (Exception e) {
			Log.w(TAG, "failed in setBootNofitcation");
    	}
    	return "fail";
    }
}  