package org.cocos2dx.bole;

import java.lang.reflect.Method;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.adjust.sdk.AdjustReferrerReceiver;
import com.google.android.gms.analytics.CampaignTrackingReceiver;

public class ReferrerReceiver extends BroadcastReceiver {
	public static String preferences = "bole_referrer_preferences";
	public static String sharedKey = "bole_share_referrer_key";
	String TAG = "ReferrerReceiver";
	@Override
	public void onReceive(Context context, Intent intent) {
		// TODO Auto-generated method stub
		 final String action = intent.getAction();

	        if (action != null && TextUtils.equals(action, "com.android.vending.INSTALL_REFERRER")) {
	            try {
	                final String referrer = intent.getStringExtra("referrer");
	                if (null!= referrer) {
		                // Parse parameters
		                Log.i(TAG, referrer);
		                context.getSharedPreferences(preferences, Context.MODE_PRIVATE).
	                    edit().putString(sharedKey, referrer).commit();
//		                String[] params = referrer.split("&");
//		                for (String p : params) {
//		                    if (p.startsWith("utm_content=")) {
//		                        final String content = p.substring("utm_content=".length());
//		                        
//		                        /**
//		                         * USE HERE YOUR CONTENT (i.e. configure the app based on the link the user clicked)
//		                         */
//		                        Log.i(TAG, content);
//		                        
//		                        break;
//		                    }
//		                }
	                }
	            } catch (Exception e) {
	            	Log.i(TAG, e.toString());
	                e.printStackTrace();
	            }

	            /**
	             * OPTIONAL: Forward the intent to Google Analytics V2 receiver
	             */
	            // new com.google.analytics.tracking.android.AnalyticsReceiver().onReceive(context, intent);
	        }
	    String tag = "Receiver";
        try{
        	Class<?> c = Class.forName("com.adjust.sdk.AdjustReferrerReceiver");
        	
        	Log.i(tag, "class is: "+c.toString());
        	Object o = c.getDeclaredConstructor().newInstance();
        	Log.i(tag, "Object is:"+o.toString());
        	Method method = c.getDeclaredMethod("onReceive", Context.class, Intent.class);
        	Log.i(tag, "Method is:"+method.toString());
        	method.invoke(o, context, intent);
        	new AdjustReferrerReceiver().onReceive(context, intent);
			new CampaignTrackingReceiver().onReceive(context, intent);
        }
        catch(Exception e) {
        	Log.i(tag, "failed to reflection adjust referrer:"+e.toString());
        }
	    
	}

}
