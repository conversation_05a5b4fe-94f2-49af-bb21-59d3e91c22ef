package org.cocos2dx.lua;

import static com.frontier.sdkbase.SDKManager.getCollectedMsg;

import android.app.Activity;
import android.app.Application;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.apm.insight.CrashType;
import com.apm.insight.ExitType;
import com.apm.insight.ICrashCallback;
import com.apm.insight.MonitorCrash;
import com.apm.insight.log.VLog;
import com.bytedance.apm.insight.ApmInsight;
import com.bytedance.apm.insight.ApmInsightAgent;
import com.bytedance.apm.insight.ApmInsightInitConfig;
import com.bytedance.apm.insight.IActivityLeakListener;
import com.bytedance.apm.insight.IDynamicParams;
import com.bytedance.apm.trace.LaunchTrace;

import org.cocos2dx.lib.Cocos2dxHelper;

import java.util.HashMap;

// APMPlus日志 : 用户的所有性能稳定性数据，回捞可以获取。或者平台配置崩溃时候上报
// AppLog是获取设备id和产生pv数据的组件
// PV是Page View的缩写，代表访问量，用来做计算崩溃率和其他的分母。在ApmPlus平台代表程序的开始一次前台会话，包括程序启动和程序在后台超过一段时间重新回到前台。
public class BytesXMonitor {
    private static final String TAG = "BytesXMonitor";
    public static MonitorCrash mMonitorCrash;
    private static boolean hasStart;
    private static boolean hasInit;
    private static final String sSDKAid = "643183";
    private static final String sToken = "fa6c524e596347009c40efe751f7272d";
    private static String mUserId = "uunkonw";

    /**
     * 初始化APM监控，不会立即采集数据，需要放到Application的onCreate执行
     *
     */
    public static void initMonitor(Application application) {
        if (hasInit) {
            return;
        }
        hasInit = true;
        initCrash(application);
        initApmInsight(application);

        // 默认同意隐私协议了
        startMonitor();
        initOnCrash();
    }

    // 初始化崩溃回调 火山监控
    public static void initOnCrash() {
        if (mMonitorCrash != null) {
            mMonitorCrash.registerCrashCallback(new ICrashCallback() {
                @Override
                public void onCrash(@NonNull CrashType crashType, @Nullable String s, @Nullable Thread thread) {
                    Log.d(TAG, "onCrash type:" + crashType + "  :" + s);

                    // 崩溃时存储，再打开时lua获取崩溃时广告主是谁
//                    if (Cocos2dxHelper.getIsInited() && Cocos2dxHelper.getActivity() != null){
//                        Cocos2dxHelper.setMMKVStringForKey("crash240815", crashType.getName());
//                        Cocos2dxHelper.setMMKVStringForKey("crash240815_ads", mMonitorCrash.getTags().get("ads"));
//                    }
                    if (Cocos2dxHelper.getLoadSyncThreadFinished() && Cocos2dxHelper.getActivity() != null){
                        Cocos2dxHelper.setUserDefaultString("crash240815", crashType.getName());
                        Cocos2dxHelper.setUserDefaultString("crash240815_ads", mMonitorCrash.getTags().get("ads"));
                    }
                }
            }, CrashType.ALL);
        }
    }

    /**
     * 崩溃监控初始化
     */
    public static void initCrash(Application application) {
        MonitorCrash.Config config = MonitorCrash.Config.app(sSDKAid)
                .token(sToken)// 设置鉴权token，可从平台应用信息处获取，token错误无法上报数据
//              .versionCode(1)// 可选，默认取PackageInfo中的versionCode
//              .versionName("1.0")// 可选，默认取PackageInfo中的versionName
//              .channel("test")// 可选，设置App发布渠道，在平台可以筛选
//              .url("www.xxx.com")// 默认不需要，私有化部署才配置上报地址
                //可选，可以设置自定义did，不设置会使用内部默认的
                .dynamicParams(new MonitorCrash.Config.IDynamicParams() {
                    @Override
                    public String getDid() {//返回空会使用内部默认的did
                        return null;
                    }

                    @Override
                    public String getUserId() {
                        return mUserId;
                    }
                })
                //可选，添加业务自定义数据，在崩溃详情页展示
                // mMonitorCrash.registerCrashCallback 这两个区别是调用时机，customdata是在收集信息过程中回调，可以通过返回值添加自定义信息上报到平台，保存崩溃时的一些重要信息。callback是在收集完成最后回调给调用方，这里不能添加数据了
              .customData(crashType -> {
                  HashMap<String, String> ret = new HashMap<>();
                  ret.put("collected_msg", getCollectedMsg());
                  ret.put("ADRewardLoading", String.valueOf(MemoryMonitor.isADRewardLoading));
                  ret.put("ADRewardPlaying", String.valueOf(MemoryMonitor.isADRewardPlaying));
                  ret.put("ADInterLoading", String.valueOf(MemoryMonitor.isADInterLoading));
                  ret.put("ADInterPlaying", String.valueOf(MemoryMonitor.isADInterPlaying));
                  ret.put("isEnterForeground", String.valueOf(BoleApplication.isActivityVisible()));
                  ret.put("ads", String.valueOf(mMonitorCrash.getTags().get("ads")));
                  return ret;
              })
                .exitType(ExitType.EXCEPTION) //上报应用退出原因，EXCEPTION会过滤USER_REQUEST类型
                .enableApmPlusLog(true) // 是否将崩溃信息等写入APMPlus日志，默认false
//                .traceDump(true) // 会显示出native堆栈，不调用这个是除了Android12之外的都默认开启。
                .keyThread("GLThread") // 对特殊线程进行火山监控里的归类
//            .crashProtect(true)  //是否开启崩溃防护，默认true
//            .autoStart(false) // 是否在初始化时自动开启监控，默认为true
//            .debugMode(true) //线下使用的日志开关，线上不要调用或设置为false
                // 可选，添加pv事件的自定义tag，可以用来筛选崩溃率计算的分母数据
                //.pageViewTags(<<Map<String, String>>>)
                .build();
        mMonitorCrash = MonitorCrash.init(application, config);
    }

    /**
     * 性能监控初始化
     */
    public static void initApmInsight(Application application) {
        //必须放到Application的onCreate里面，会注册监听生命周期，不涉及数据采集和隐私合规问题
        ApmInsight.getInstance().init(application);
        //初始化自定日志，配置自定义日志最大占用磁盘，内部一般配置20,代表最大20M磁盘占用。1.4.1版本开始存在这个api
        VLog.init(application, 20);
    }

    /**
     * 启动APM监控，在隐私合规后调用，开始采集数据
     */
    public static void startMonitor() {
        if (hasStart) {
            return;
        }
        hasStart = true;

        // 如果初始化崩溃组件时未设置autoStart参数或者设置为true，将自动开启监控，不需要调用start方法。
//        if (mMonitorCrash != null) {
//            mMonitorCrash.start();
//        }

        //在同意隐私合规后调用，启动性能组件监控
        ApmInsightInitConfig.Builder builder = ApmInsightInitConfig.builder();
        //必填：设置分配的appid
        builder.aid(sSDKAid);
        //必填：设置平台的app_token
        builder.token(sToken);
        //是否开启卡顿功能
        builder.blockDetect(true);
        //是否开启严重卡顿功能
        builder.seriousBlockDetect(true);
        //是否开启流畅性和丢帧
        builder.fpsMonitor(true);
        //控制是否打开WebVeiw监控
        builder.enableHybridMonitor(true);
        //控制是否打开内存监控
        builder.memoryMonitor(true);
        //控制是否打开电量监控
        builder.batteryMonitor(true);
        //是否打印日志，注：线上release版本要配置为false
        builder.debugMode(false);
        //支持用户自定义user_id把平台数据和自己用户关联起来，可以不配置。1.4.5版本后使用setDynamicParams()方法通过getUserId()回调设置
//        builder.userId("user_id");
        //私有化部署：配置数据上报的域名 （私有化部署才需要配置，内部有默认域名），测试支持设置http://www.xxx.com  默认是https协议
//        builder.defaultReportDomain("www.xxx.com");
        //设置渠道。1.3.16版本增加接口
        builder.channel("google play");
        //打开自定义日志回捞能力，1.4.1版本新增接口
        builder.enableLogRecovery(true);
        //控制是否打开cpu监控能力
        builder.cpuMonitor(true);
        //打开磁盘监控
        builder.diskMonitor(true);
        //打开流量监控
        builder.trafficMonitor(true);
        //控制是否打开用户使用时长的监控
        builder.operateMonitor(true);
        //控制是否打开启动监控，开启需要同时配置apm-plugin插件
        builder.startMonitor(true);
        //控制是否打开页面Activity耗时监控，开启需要同时配置apm-plugin插件
        builder.pageMonitor(true);
        //控制是否打开网络监控，开启需要同时配置apm-plugin插件
        builder.netMonitor(true);
        //打开APMPlus日志能力，可以通过回捞获取APMPlus日志
        builder.enableAPMPlusLocalLog(true);
        //打开全链路监控的能力，会在请求里注入trace_id，并在网络监控上报到服务器，通过trace_id可以和接入了APMPlus服务端监控的后端数据打通，达到全链路监控的目标
        // builder.enableNetTrace(true);
        builder.detectActivityLeak(new IActivityLeakListener() {
            @Override
            public void onActivityLeaked(Activity activity) {
                Log.e("Activity Leak", activity.getLocalClassName());
            }
        });
        //设置数据和Rangers Applog数据打通，设备标识did必填。1.3.16版本增加接口
        builder.setDynamicParams(new IDynamicParams() {
            @Override
            public String getUserUniqueID() {
                //可选。依赖AppLog可以通过AppLog.getUserUniqueID()获取，否则可以返回null。
                return null;
            }

            @Override
            public String getAbSdkVersion() {
                //可选。如果依赖AppLog可以通过 AppLog.getAbSdkVersion()获取，否则可以返回null。
                return null;
            }

            @Override
            public String getSsid() {
                //可选。依赖AppLog可以通过AppLog.getSsid()获取，否则可以返回null。
                return null;
            }

            @Override
            public String getDid() {
                //1.4.0版本及以上，可选，其他版本必填。设备的唯一标识，如果依赖AppLog可以通过 AppLog.getDid() 获取，也可以自己生成。
                // 如果业务已经自定义：直接根据自定义的DID配置，即初始化时getDid()返回的值
                // 如果业务没有自定义：调用ApmInsightAgent.getDid()接口，获取内部默认的DID。
//                Log.d("aaa", ApmInsightAgent.getDid());
//                return ApmInsightAgent.getDid();
                return null;
            }

            @Override
            public String getUserId() {
                //1.4.5.cn版本增加的接口
                return mUserId;
            }
        });
        ApmInsight.getInstance().start(builder.build());
    }

    public static void setUserId(String uid) {
        mUserId = uid;
    }

    // 主动上报Vlog日志文件(自定义日志)
    // minsAgo : 上传当前时间前 minsAgo 分钟的vlog日志。
    public static void uploadVlog(int minsAgo) {
        ApmInsightAgent.uploadVlog(System.currentTimeMillis() - 1000L * 60 * minsAgo, System.currentTimeMillis());
    }

    // 主动上报 APMPlus 日志
    // minsAgo : 传当前时间前 minsAgo 分钟的APMPlus日志。
    public static void uploadAPMPlusLog(int minsAgo) {
        ApmInsightAgent.uploadAPMPlusLog(System.currentTimeMillis() - 1000L * 60 * minsAgo, System.currentTimeMillis(), null);
    }

    // 崩溃组件的自定义维度
    public static void addTags(String key, String value) {
        if (mMonitorCrash != null) {
            mMonitorCrash.addTags(key, value);
        }
    }

    public static void removeTags(String key) {
        if (mMonitorCrash != null) {
            mMonitorCrash.getTags().remove(key);
        }
    }

    // 性能组件的自定义维度：上报的tag可以在日志查询或者原始日志文件里查询。卡顿分析可用
    public static void addPerfTag(String key, String value) {
        ApmInsightAgent.addPerfTag(key, value);
    }

    // 性能组件的自定义维度：上报的tag可以在日志查询或者原始日志文件里查询。卡顿分析可用
    public static void removePerfTag(String key, String value) {
        ApmInsightAgent.removePerfTag(key, value);
    }

    // 启动监控支持自定义打点
    public static void startSpan(String key) {
        LaunchTrace.startSpan(key);
    }

    // 启动监控支持自定义打点
    public static void endSpan(String key) {
        LaunchTrace.endSpan(key);
    }

    // 自定义日志
    public static void setVLogV(String tag, String msg) {
        VLog.v(tag, msg);
    }

    public static void setVLogD(String tag, String msg) {
        VLog.d(tag, msg);
    }

    public static void setVLogI(String tag, String msg) {
        VLog.i(tag, msg);
    }

    public static void setVLogW(String tag, String msg) {
        VLog.w(tag, msg);
    }

    public static void setVLogE(String tag, String msg) {
        VLog.e(tag, msg);
    }
}
