package org.cocos2dx.lua;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.ImageView;

import androidx.appcompat.app.AppCompatActivity;

import com.grandegames.slots.dafu.casino.R;

public class SplashActivity extends AppCompatActivity {

    private static Handler handler = new Handler();
    private ImageView logo;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);
        logo = findViewById(R.id.logo);
        int visibility = getWindow().getDecorView().getSystemUiVisibility();
        visibility |= View.SYSTEM_UI_FLAG_HIDE_NAVIGATION;
        visibility |= View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
        getWindow().getDecorView().setSystemUiVisibility(visibility);
//        runAnimation();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                openGameActivity();
            }
        },1000);
    }

    private void openGameActivity()
    {
        final String[] configs = {"org.cocos2dx.lua.AppActivity", "org.cocos2dx.lua.AppActivityAlias"};
        PackageManager pm = getPackageManager();
        int index = 0;
        for(int i = 0; i < configs.length; ++ i) {
            String config = configs[i];
            int ret = pm.getComponentEnabledSetting(new ComponentName(this, config));
            if(ret == PackageManager.COMPONENT_ENABLED_STATE_DEFAULT || ret == PackageManager.COMPONENT_ENABLED_STATE_ENABLED) {
                index = i;
                break;
            }
        }
        Intent intent = null;
        if(index == 0) {
            intent = new Intent(SplashActivity.this, AppActivity.class);
        } else {
            intent = new Intent();
            intent.setComponent(new ComponentName(getPackageName(), configs[index]));
        }
        startActivity(intent);
        finish();
        overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
    }

    private void runAnimation()
    {
        AlphaAnimation anim = new AlphaAnimation(1.0f, 0.0f);
        anim.setDuration(1000);
        anim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                logo.setAlpha(0.0f);
                openGameActivity();
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                logo.startAnimation(anim);
            }
        }, 500);
    }
}