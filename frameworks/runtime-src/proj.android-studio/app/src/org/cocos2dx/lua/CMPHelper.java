package org.cocos2dx.lua;

import android.util.Log;

import org.cocos2dx.lib.Cocos2dxActivity;
import org.cocos2dx.lib.Cocos2dxHelper;
import org.cocos2dx.lib.Cocos2dxLuaJavaBridge;
import org.cocos2dx.lib.GLTaskMonitor;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

import net.consentmanager.sdk.*;
import net.consentmanager.sdk.consentlayer.model.*;
import net.consentmanager.sdk.common.callbacks.*;

public class CMPHelper {
    static String tag = "CMPHelper";

    private static CmpManager mCMPManager = null;
    public static void initCMPManager(String paramJson, int luaCallback) {
        try {
            JSONObject o = new JSONObject(paramJson);
            String id = o.getString("id");
            String domain = o.getString("domain");
            String appName = o.getString("appName");
            String lang = o.getString("lang");
            Integer timeout = o.getInt("timeout");
            JSONArray purposeJSONArray = o.getJSONArray("purposeList");
            ArrayList<String> purposeList = new ArrayList<>();
            for (int i = 0; i < purposeJSONArray.length(); i++) {
                purposeList.add(purposeJSONArray.getString(i));
            }
            Log.d(tag, "initCMPManager");
            CmpConfig cmpConfig = CmpConfig.INSTANCE;
            cmpConfig.setId(id);
            cmpConfig.setDomain(domain);
            cmpConfig.setAppName(appName);
            cmpConfig.setLanguage(lang);
            cmpConfig.setTimeout(timeout);
            OnOpenCallback openListener = new OnOpenCallback() {
                @Override
                public void onConsentLayerOpened() {
                    Log.d(tag, "onConsentLayerOpened");
                }
            };
            OnCloseCallback closeListener = new OnCloseCallback() {
                @Override
                public void onConsentLayerClosed() {
                    Log.d(tag, "onConsentLayerClosed");
                    try {
                        JSONObject result = new JSONObject();
                        for (String purpose:purposeList) {
                            if (mCMPManager.hasPurposeConsent(purpose)) {
                                Log.d(tag, String.format("has purpose %s", purpose));
                                result.put(purpose, true);
                            }
                            else{
                                Log.d(tag, String.format("no purpose %s", purpose));
                                result.put(purpose, false);
                            }
                        }
                        if(((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getGLSurfaceView() != null && ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getGLSurfaceView().getCocos2dxRenderNativeInitCompleted()) {
                            Cocos2dxHelper.runOnGLThread(new GLTaskMonitor(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        if(luaCallback != -1) {
                                            Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, result.toString());
                                            Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
                                        }
                                    } catch (Exception e) {
                                        Log.e(tag, e.toString());
                                        e.printStackTrace();
                                    }
                                }
                            }, "CM_closeListener"));
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            };
            mCMPManager = CmpManager.createInstance(Cocos2dxHelper.getActivity(), cmpConfig, openListener, closeListener);

            mCMPManager.openConsentLayer(Cocos2dxHelper.getActivity());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
    public static void getPurpose(String paramJson, int luaCallback) {
        try {
            Log.d(tag, String.format("getPurpose paramJson=%s", paramJson));
            JSONObject o = new JSONObject(paramJson);
            JSONArray purposeJSONArray = o.getJSONArray("purposeList");
            ArrayList<String> purposeList = new ArrayList<>();
            for (int i = 0; i < purposeJSONArray.length(); i++) {
                purposeList.add(purposeJSONArray.getString(i));
            }
            JSONObject result = new JSONObject();
            for (String purpose:purposeList) {
                if (mCMPManager.hasPurposeConsent(purpose)) {
                    Log.d(tag, String.format("has purpose %s", purpose));
                    result.put(purpose, true);
                }
                else{
                    Log.d(tag, String.format("no purpose %s", purpose));
                    result.put(purpose, false);
                }
            }
            if(((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getGLSurfaceView() != null && ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getGLSurfaceView().getCocos2dxRenderNativeInitCompleted()) {
                Cocos2dxHelper.runOnGLThread(new GLTaskMonitor(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if(luaCallback != -1) {
                                Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, result.toString());
                                Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
                            }
                        } catch (Exception e) {
                            Log.e(tag, e.toString());
                            e.printStackTrace();
                        }
                    }
                }, "CM_getPurpose"));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
