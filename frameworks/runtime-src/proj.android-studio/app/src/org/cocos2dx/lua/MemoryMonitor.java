package org.cocos2dx.lua;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import android.os.Debug;
import android.os.Environment;
import android.os.StatFs;
import android.util.Log;

import org.cocos2dx.lib.Cocos2dxHelper;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR> sunyungao
 * @date ： 2023/12/07
 * @description ：
 * 用于检查内存状况
 * 参考： https://blog.csdn.net/hexingen/article/details/130502109
 */
public class MemoryMonitor {
    private static final String TAG = "MemoryMonitor";

    /**
     * 统计内存状况：
     *
     * @return
     */
    public static JSONObject statisticsMemory() {
        JSONObject array = new JSONObject();
        try {
            JSONObject json1 = statisticsJVMMemory();
            array.put("JVM", json1);
            // Log.i(TAG, "cocos2d-x list jvm: " + json1.toString());

            JSONObject json2 = statisticsNativeMemory();
            array.put("Native", json2);
            // Log.i(TAG, "cocos2d-x list native: " + json2.toString());

            JSONObject json3 = statisticsProcessMemory();
            array.put("Process", json3);
            // Log.i(TAG, "cocos2d-x list app process: " + json3.toString());

            JSONObject json4 = statisticsSystemMemory();
            array.put("System", json4);
            // Log.i(TAG, "cocos2d-x list system: " + json4.toString());

            int fdCount = listFd();
            array.put("FD", String.valueOf(fdCount));
            // Log.i(TAG, "cocos2d-x list fd: " + fdCount);

            JSONObject json5 = statisticsAPPSummaryMemory();
            array.put("summary", json5);
            // Log.i(TAG, "cocos2d-x list summary: " + json5.toString());

            JSONObject json6 = statisticsSDStorage();
            array.put("SDStorage", json6);
            // Log.i(TAG, "cocos2d-x list SDStorage: " + json6.toString());

            JSONObject json7 = statisticsDeviceMemory();
            array.put("Device", json7);
            // Log.i(TAG, "cocos2d-x list Device: " + json7.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return array;
    }

    // 查看当前FD数量
    public static int listFd() {
        File fdFile = new File("/proc/" + android.os.Process.myPid() + "/fd/");
        File[] files = fdFile.listFiles(); // 列出当前目录下所有的文件
        if (files == null){
            return 0;
        }
        return files.length; // 进程中的fd数量
    }

    // 内存总结，与profiler里内存相同
    public static JSONObject statisticsAPPSummaryMemory() {
        // "javaHeap":"14mB","nativeHeap":"18mB","code":"69mB","stack":"0mB","graphics":"93mB","privateOther":"8mB","system":"15mB","swap":"19mB","total":"236mB"
        JSONObject jsonObject = new JSONObject();
        Debug.MemoryInfo memoryInfo = new Debug.MemoryInfo();
        Debug.getMemoryInfo(memoryInfo);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            String javaHeap = memoryInfo.getMemoryStat("summary.java-heap"); // Java 堆内存使用量。
            String nativeHeap = memoryInfo.getMemoryStat("summary.native-heap"); // Native 堆内存使用量。
            String code = memoryInfo.getMemoryStat("summary.code"); // 代码占用的内存量，包括所有 .so 文件和 APK 中的所有 .dex 文件。
            String stack = memoryInfo.getMemoryStat("summary.stack"); // 栈内存使用量。 包括创建线程
            String graphics = memoryInfo.getMemoryStat("summary.graphics"); // 以字节为单位的图形内存使用量，包括 GL 和其他图形缓冲区。
            String privateOther = memoryInfo.getMemoryStat("summary.private-other"); // 除堆、代码、栈和图形之外的其它私有内存使用量。
            String system = memoryInfo.getMemoryStat("summary.system"); // 系统 RAM 使用量，包括内核中不可清除的内存和 ZRAM，但不包括设备的总 RAM。
            String swap = memoryInfo.getMemoryStat("summary.total-swap"); // 应用程序的 Swap（交换空间）使用量。
            // 最后还有这个 summary.total-pss：应用程序的总 PSS 内存使用量。

            // 创建 DecimalFormatSymbols 对象并设置小数分隔符
            DecimalFormatSymbols symbols = new DecimalFormatSymbols();
            symbols.setDecimalSeparator('.');  // 设置小数点为 '.'
            DecimalFormat df = new DecimalFormat("0.00", symbols);

            float javaHeapValue = (float)Long.parseLong(javaHeap) / 1024;
            float nativeHeapValue = (float)Long.parseLong(nativeHeap) / 1024;
            float codeValue = (float)Long.parseLong(code) / 1024;
            float stackValue = (float)Long.parseLong(stack) / 1024;
            float graphicsValue = (float)Long.parseLong(graphics) / 1024;
            float privateOtherValue = (float)Long.parseLong(privateOther) / 1024;
            float systemValue = (float)Long.parseLong(system) / 1024;
            float swapValue = (float)Long.parseLong(swap) / 1024;
            float totalValue = javaHeapValue + nativeHeapValue + codeValue + stackValue + graphicsValue + privateOtherValue + systemValue + swapValue;

            try {
                jsonObject.put("javaHeap", df.format(javaHeapValue) + "mB");
                jsonObject.put("nativeHeap", df.format(nativeHeapValue) + "mB");
                jsonObject.put("code", df.format(codeValue) + "mB");
                jsonObject.put("stack", df.format(stackValue) + "mB");
                jsonObject.put("graphics", df.format(graphicsValue) + "mB");
                jsonObject.put("privateOther", df.format(privateOtherValue) + "mB");
                jsonObject.put("system", df.format(systemValue) + "mB");
                jsonObject.put("swap", df.format(swapValue) + "mB");
                jsonObject.put("total", df.format(totalValue) + "mB");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return jsonObject;
    }

    // 获取设备sd卡存储
    public static JSONObject statisticsSDStorage() {
        JSONObject jsonObject = new JSONObject();
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long freeBlocks = stat.getAvailableBlocks();
        long freeStorage = freeBlocks*blockSize/1024 / 1024;

        long totalBlocks = stat.getBlockCount();
        long totalStorage = totalBlocks*blockSize/1024 / 1024;
        try {
            jsonObject.put("freeStorage", freeStorage + "mB");
            jsonObject.put("totalStorage", totalStorage + "mB");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return jsonObject;
    }

    // 设备总内存
    public static JSONObject statisticsDeviceMemory(){
        JSONObject jsonObject = new JSONObject();
        Activity a = Cocos2dxHelper.getActivity();
        if ( a != null) {
            ActivityManager manager = (ActivityManager) a.getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
            manager.getMemoryInfo(mi);

            try {
                jsonObject.put("totalMem", (mi.totalMem / 1024 / 1024) + "mB");
                jsonObject.put("availMem", (mi.availMem / 1024 / 1024) + "mB");
                jsonObject.put("threshold", (mi.threshold / 1024 / 1024) + "mB");
                jsonObject.put("lowMemory", mi.lowMemory);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return jsonObject;
    }

    // 设备总内存详细数据，用于确定是否是低内存状态
    public static String statisticsDeviceMemoryDetail(){
        String detailStr = "";
        Activity a = Cocos2dxHelper.getActivity();
        if ( a != null) {
            ActivityManager manager = (ActivityManager) a.getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
            manager.getMemoryInfo(mi);

            detailStr = mi.totalMem + ":" + mi.availMem + ":" + mi.threshold + ":" + mi.lowMemory;
        }

        return detailStr;
    }

    /**
     * 当前进程中native层的内存
     *
     * @return
     */
    public static JSONObject statisticsNativeMemory() {
        JSONObject jsonObject = new JSONObject();
        float totalNative = (float)Debug.getNativeHeapSize() / 1024 / 1024; // 当前进程中native层申请的堆内存，会随着时间而变化，加大或者减少
        float useNative = (float)Debug.getNativeHeapAllocatedSize() / 1024 / 1024;//当进程中native层中已分配堆内存,而SummaryMemory中的native是实际使用的PSS
        float freeNative = (float)Debug.getNativeHeapFreeSize() / 1024 / 1024;//当前进程中native层中剩余的堆内存
        try {
            // 创建 DecimalFormatSymbols 对象并设置小数分隔符
            DecimalFormatSymbols symbols = new DecimalFormatSymbols();
            symbols.setDecimalSeparator('.');  // 设置小数点为 '.'
            DecimalFormat df = new DecimalFormat("0.00", symbols);
            jsonObject.put(MemoryKeys.NativeKeys.key_native_total, df.format(totalNative) + " mB");
            jsonObject.put(MemoryKeys.NativeKeys.key_native_use, df.format(useNative) + " mB");
            jsonObject.put(MemoryKeys.NativeKeys.key_native_free, df.format(freeNative) + " mB");
            jsonObject.put(MemoryKeys.NativeKeys.key_free_rate, getTwoDecimalPlaces((freeNative / totalNative * 100)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    public interface ProcCmd {
        String cmd_system_meminfo = "/proc/meminfo"; //查看手机当前处理器内存状况
        String cmd_app_status = "/proc/self/status";//当前进程中状况，内存、线程、fd等等；
        String cmd_app_limit = "/proc/self/limits";//当前进程的限制，线程、fd的最大峰值
    }


    /**
     * 统计系统设备内存
     * 通过命令 adb shell dumpsys meminfo com.grandegames.slots.dafu.casino
     * MemTotal: 所有可用 RAM 大小（单位：KB），跟 statisticsDeviceMemory 获取到的一样
     * MemFree：当前空闲的 RAM 大小（单位：KB）
     * MemAvailable： 算法算出可用内存，包含可回收使用的内存 ，通常看这个
     *
     * @return
     */
    public static JSONObject statisticsSystemMemory() {
        final JSONObject json = new JSONObject();
        //"/proc/meminfo"
        File file = new File(ProcCmd.cmd_system_meminfo);
        readFileLine(file, new Block() {
            long total, available;

            @Override
            public void block(String line) {
                try {
                    String s = null;
                    if (line.startsWith(MemoryKeys.SystemKeys.key_mem_total)) {
                        //手机处理器的内存，运行多少G
                        s = line;
                    } else if (line.startsWith(MemoryKeys.SystemKeys.key_mem_free)) {
                        //手机系统剩余内存,不包含可回收的内存。[MemTotal-MemFree]就是已被用掉的内存
                        s = line;
                    } else if (line.startsWith(MemoryKeys.SystemKeys.key_mem_available)) {
                        //手机系统可用内存：动态计算出的可用内存，包含mem_free + 可回收使用的内存，该值是一个估值。
                        s = line;
                    }/*else if (line.startsWith(MemoryKeys.SystemKeys.key_commit_limit)){
                        s=line;
                    }
                    else if (line.startsWith(MemoryKeys.SystemKeys.key_committed_as)){
                        s=line;
                    }*/
                    if (s != null) {
                        String[] array = s.split(":");
                         String value = array[1].trim().split(" kB")[0];
                         long size = Integer.parseInt(value) / 1024;
                         json.put(array[0], size + " mB");
                        if (s.startsWith(MemoryKeys.SystemKeys.key_mem_total)) {
                            total = size;
                        }
                        if (s.startsWith(MemoryKeys.SystemKeys.key_mem_available)) {
                            available = size;
                        }
                        if (total != 0 && available != 0) {
                            json.put(MemoryKeys.SystemKeys.key_free_rate, getTwoDecimalPlaces((float) available / total * 100));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        return json;
    }

    /**
     * 计算进程中内存状况和线程状况：
     * FDSize: 128  // 当前分配的文件描述符，这个值不是当前进程使用文件描述符的上线
     * VmPeak:  4403108 kB    // 当前进程运行过程中所占用内存的峰值
     * VmSize:  4402056 kB    // 已用逻辑空间地址，虚拟内存大小。整个进程使用虚拟内存大小，是VmLib, VmExe, VmData, 和 VmStk的总和。
     * VmLck:         0 kB
     * VmPin:         0 kB
     * VmHWM:     49108 kB    // 程序得到分配到物理内存的峰值
     * VmRSS:     48920 kB    // 程序现在正在使用的物理内存
     * RssAnon:            9268 kB
     * RssFile:           39540 kB
     * RssShmem:            112 kB
     * VmData:  1737808 kB        // 所占用的虚拟内存
     * VmStk:      8192 kB        // 任务在用户态的栈的大小 (stack_vm)
     * VmExe:        20 kB        // 程序所拥有的可执行虚拟内存的大小，代码段，不包括任务使用的库 (end_code-start_code)
     * VmLib:    163804 kB        // 被映像到任务的虚拟内存空间的库的大小 (exec_lib)
     * VmPTE:      1000 kB        // 该进程的所有页表的大小，单位：kb
     * Threads:        17        // 当前的线程数
     *
     * @return
     */
    public static JSONObject statisticsProcessMemory() {

        final JSONObject json = new JSONObject();
        // Linux 的/proc/self/status文件。这个并不是一个真实存在的文件，而为 Linux 的一个内核接口
        File file = new File(ProcCmd.cmd_app_status);
        readFileLine(file, new Block() {
            @Override
            public void block(String line) {
                try {
                    String s = null;
                    if (line.startsWith(MemoryKeys.ProcessKeys.key_threads)) {
                        //进程中线程的数量
                        s = line;
                    } else if (line.startsWith(MemoryKeys.ProcessKeys.key_vm_size)) {
                        //整个进程中虚拟内存的总和(= VmLib+VmExe+VmData+VmStk),会动态变化增加
                        s = line;
                    } else if (line.startsWith(MemoryKeys.ProcessKeys.key_vm_rss)) {
                        // 进程中当前物理内存,即系统实际在物理内存上分配给程序的内存
                        s = line;
                    } else if (line.startsWith(MemoryKeys.ProcessKeys.key_vm_data)) {
                        s = line;
                    } else if (line.startsWith(MemoryKeys.ProcessKeys.key_fd_size)) {
                        s = line;
                    }else if (line.startsWith(MemoryKeys.ProcessKeys.key_vm_peek)){
                        s = line;
                    }
                    if (s != null) {
                        String[] array = s.split("\\t");
                        String name = array[0].split(":")[0];
                        String value = array[1].trim();
                        //进程中虚拟内存
                        json.put(name, value);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        return json;
    }

    private interface Block {
        void block(String line);
    }

    private static void readFileLine(File file, Block block) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            while (true) {
                String line = reader.readLine();
                if (line == null) {
                    break;
                } else {
                    if (block != null) {
                        block.block(line);
                    }
                    continue;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(reader);
        }
    }

    private static void close(Closeable closeable) {
        try {
            if (closeable == null) {
                return;
            }
            closeable.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 用于统计jvm 内存:
     * 1.最大限制内存
     * 2.申请的内存
     * 3.(申请的内存中)剩余使用的内存
     * 4.已使用的内存
     * 5.内存使用率
     *
     */
    public static JSONObject statisticsJVMMemory() {
        JSONObject json = new JSONObject();
        Runtime runtime = Runtime.getRuntime();
        //进程中最大jvm 内存大小
        float max_memory = (float)runtime.maxMemory() / 1024 / 1024;
        //进程中申请的jvm内存大小，不等用于一定分配那么多内存(会随着时间变化而变化)
        float apply_memory = (float)runtime.totalMemory() / 1024 / 1024;
        //进程中申请内存中可使用的jvm内存大小
        float free_memory = (float)runtime.freeMemory() / 1024 / 1024;
        //进程中已经使用的jvm 内存
        float use_memory = apply_memory - free_memory;
        //计算出jvm 的内存使用率,超过0.8就需要警惕
        float use_memory_rate = ((float) use_memory) / max_memory * 100;
        //真正可用的内存,包含剩余可申请的内存
        float actual_free_memory = max_memory - use_memory;
        try {
            // 创建 DecimalFormatSymbols 对象并设置小数分隔符
            DecimalFormatSymbols symbols = new DecimalFormatSymbols();
            symbols.setDecimalSeparator('.');  // 设置小数点为 '.'
            DecimalFormat df = new DecimalFormat("0.00", symbols);
            final String kB = " mB";
            json.put(MemoryKeys.JvmMemoryKeys.key_max_memory, df.format(max_memory) + kB);
            json.put(MemoryKeys.JvmMemoryKeys.key_apply_memory, df.format(apply_memory) + kB);
            json.put(MemoryKeys.JvmMemoryKeys.key_free_memory, df.format(free_memory) + kB);
            json.put(MemoryKeys.JvmMemoryKeys.key_use_memory, df.format(use_memory) + kB);
            json.put(MemoryKeys.JvmMemoryKeys.key_actual_free_memory, df.format(actual_free_memory) + kB);
            json.put(MemoryKeys.JvmMemoryKeys.key_use_memory_rate, getTwoDecimalPlaces(use_memory_rate));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return json;
    }

    private static String getTwoDecimalPlaces(float value) {
        return String.format("%.2f", value) + "%";
    }

    interface MemoryKeys {

        interface JvmMemoryKeys {
            String key_max_memory = "maxMemory";
            String key_free_memory = "freeMemory";
            String key_use_memory = "useMemory";
            String key_apply_memory = "totalMemory";
            String key_actual_free_memory = "actualFreeMemory";
            String key_use_memory_rate = "use_memory_rate";
        }

        interface ProcessKeys {
            String key_vm_size = "VmSize";//进程中虚拟内存总值
            String key_vm_rss = "VmRSS"; //进程中已经使用的物理内存
            String key_threads = "Threads";//当前进程中线程个数
            String key_fd_size = "FDSize"; //当前进程中fd 资源个数(包含file、socket)
            String key_vm_data = "VmData";// 当前进程中
            String key_vm_peek = "VmPeak";
        }

        interface SystemKeys {

            String key_mem_total = "MemTotal";
            String key_mem_free = "MemFree";
            String key_mem_available = "MemAvailable";
            String key_commit_limit = "CommitLimit"; // committed_as的阀值,限制最大值
            String key_committed_as = "Committed_AS";//所有进程申请内存总和,超过CommitLimit 越多越容易oom
            String key_free_rate = "free_memory_rate";// 手机可用内存率
        }

        interface NativeKeys {
            String key_native_total = "TotalNative";
            String key_native_free = "freeNative";
            String key_native_use = "useNative";
            String key_free_rate = "free_memory_rate";// 手机可用内存率
        }
    }

    // --------------------------------------------------- 崩溃 ANR 统计相关逻辑 ---------------------------------------------------
    private static final String bl_crash_filename = "bl_crash.log";
    private static final String bl_crash_info_filename = "bl_crash_info.json";

    private static ConcurrentLinkedQueue<MemoryMonitor.CrashInfoPolyMessage> mCrashInfoList = new ConcurrentLinkedQueue<>(); // 定时存储的数据信息

    // 存储崩溃内容
    public static void cacheCrashOrANRData(String errorStack){
        try {
            if (Cocos2dxHelper.getActivity() != null) {
                // 打开私有文件输出流
                FileOutputStream fos = Cocos2dxHelper.getActivity().openFileOutput(bl_crash_filename, Context.MODE_PRIVATE);
                // 创建输出流写入器
                OutputStreamWriter osw = new OutputStreamWriter(fos);
                // 写入内容
                osw.write(errorStack);
                // 关闭写入器和文件输出流
                osw.close();
                fos.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 读取崩溃文件内容
    public static String readCrashOrANRData() {
        StringBuilder content = new StringBuilder();
        try {
            // 打开私有文件输入流
            FileInputStream fis = Cocos2dxHelper.getActivity().openFileInput(bl_crash_filename);
            // 创建输入流读取器
            InputStreamReader isr = new InputStreamReader(fis);
            char[] inputBuffer = new char[1024];
            int charRead;
            // 读取文件内容
            while ((charRead = isr.read(inputBuffer)) > 0) {
                String readstring = String.copyValueOf(inputBuffer, 0, charRead);
                content.append(readstring);
            }
            // 关闭读取器和文件输入流
            isr.close();
            fis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return content.toString();
    }

    // 清除崩溃文件内容
    public static void clearCrashOrANRData(){
        cacheCrashOrANRData("");
    }

    // 进行一次数据存储，在crash 或者 ANR 时收集此数据到服务器
    // maxCount : 最多存储多少条
    // param : 附加信息，比如lua额外提供的数据
    public static void collectMemoryInfo(int maxCount, String param){
        MemoryMonitor.CrashInfoPolyMessage apm = mCrashInfoList.size() >= maxCount ? mCrashInfoList.poll() : new MemoryMonitor.CrashInfoPolyMessage();
        if (apm == null){
            return;
        }
        apm.updateData(param);
        mCrashInfoList.offer(apm);
    }

    public static ArrayList<String> getCrashInfoList(){
        ArrayList<String> infoArray = new ArrayList<>();
        for (CrashInfoPolyMessage element : mCrashInfoList) {
            infoArray.add(element.JSONDataToString());
        }

        return infoArray;
    }

    // 存储崩溃内容
    public static void cacheMemoryInfo(ArrayList<String> infoArray){
        try {
            // 打开私有文件输出流
            FileOutputStream fos = Cocos2dxHelper.getActivity().openFileOutput(bl_crash_info_filename, Context.MODE_PRIVATE);
            // 创建输出流写入器
            OutputStreamWriter osw = new OutputStreamWriter(fos);
            // 写入内容
            if (infoArray != null){
                osw.write("[");
                for (int i = 0; i < infoArray.size(); i++){
                    osw.append(infoArray.get(i));
                    if (i < infoArray.size() - 1){
                        osw.append(",\n");
                    }
                }
                osw.append("]");
            }else{
                osw.write("");
            }

            // 关闭写入器和文件输出流
            osw.close();
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 读取崩溃文件内容
    public static String readMemoryInfo() {
        StringBuilder content = new StringBuilder();
        try {
            // 打开私有文件输入流
            FileInputStream fis = Cocos2dxHelper.getActivity().openFileInput(bl_crash_info_filename);
            // 创建输入流读取器
            InputStreamReader isr = new InputStreamReader(fis);
            char[] inputBuffer = new char[1024];
            int charRead;
            // 读取文件内容
            while ((charRead = isr.read(inputBuffer)) > 0) {
                String readstring = String.copyValueOf(inputBuffer, 0, charRead);
                content.append(readstring);
            }
            // 关闭读取器和文件输入流
            isr.close();
            fis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return content.toString();
    }

    // 清除崩溃文件内容
    public static void clearMemoryInfo(){
        cacheMemoryInfo(null);
    }


    /**
     * 每条聚合消息的消息类
     */
    private static class CrashInfoPolyMessage {
        // 更新数据
        void updateData(String p_param){
            allJSONObject = new JSONObject();
            try {
                JSONObject json5 = statisticsAPPSummaryMemory();
                allJSONObject.put("summary", json5);

                JSONObject json7 = statisticsDeviceMemory();
                allJSONObject.put("Device", json7);

//                JSONObject json4 = statisticsSystemMemory();
//                allJSONObject.put("System", json4);

                JSONObject json6 = statisticsSDStorage();
                allJSONObject.put("SDStorage", json6);

                if (!Objects.equals(p_param, "")){
                    allJSONObject.put("param", p_param);
                }
            }catch (JSONException e){
                e.printStackTrace();
            }
        }

        private JSONObject allJSONObject;

        String JSONDataToString(){
            return allJSONObject.toString();
        }
    }

    // --------------------------------------------------- 崩溃 ANR 统计相关逻辑  广告相关 ---------------------------------------------------
    public static boolean isADRewardLoading = false;
    public static boolean isADRewardPlaying = false;
    public static boolean isADInterLoading = false;
    public static boolean isADInterPlaying = false;

    public static void updateADState(int eventCode){
        Log.d(TAG, "updateADState " + eventCode);
        if (eventCode == 1010){
            isADRewardLoading = true;
        } else if (eventCode == 1008 || eventCode == 1009) {
            isADRewardLoading = false;
        } else if (eventCode == 1000) {
            isADRewardPlaying = true;
        } else if (eventCode == 1001 || eventCode == 1006) {
            isADRewardPlaying = false;
        } else if (eventCode == 2007){
            isADInterLoading = true;
        } else if (eventCode == 2000 || eventCode == 2001) {
            isADInterLoading = false;
        } else if (eventCode == 2002) {
            isADInterPlaying = true;
        } else if (eventCode == 2003 || eventCode == 2005) {
            isADInterPlaying = false;
        }
    }
}

// 小米手机正常时数据，除了磁盘空间不足
// JVM={"maxMemory":"256.00 mB","totalMemory":"17.78 mB","freeMemory":"3.02 mB","useMemory":"14.76 mB","actualFreeMemory":"241.24 mB","use_memory_rate":"5.77%"}
// Native={"TotalNative":"46.54 mB","useNative":"43.94 mB","freeNative":"2.60 mB","free_memory_rate":"5.59%"}
// Process={"FDSize":"512","VmPeak":"33731568 kB","VmSize":"25555124 kB","VmRSS":"266960 kB","VmData":"1584600 kB","Threads":"113"}
// System={"MemTotal":"7383 mB","MemFree":"97 mB","MemAvailable":"4414 mB","free_memory_rate":"59.79%"}
// FD=269
// summary={"javaHeap":"22.70mB","nativeHeap":"35.08mB","code":"73.98mB","stack":"4.45mB","graphics":"106.35mB","privateOther":"42.95mB","system":"12.80mB","swap":"17.93mB","total":"316.24mB"}
// SDStorage={"freeStorage":"0mB","totalStorage":"231625mB"}
// Device={"totalMem":"7383mB","availMem":"4505mB"}