package org.cocos2dx.lua;

//import MultiDexApplication;

import android.app.Activity;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;

import androidx.multidex.MultiDexApplication;

import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustConfig;
import com.adjust.sdk.LogLevel;
import com.adjust.sdk.OnAdidReadListener;
import com.adjust.sdk.OnDeferredDeeplinkResponseListener;
import com.adjust.sdk.OnAttributionReadListener;
import com.adjust.sdk.AdjustAttribution;
import com.frontier.sdkbase.SDKManager;
//import com.grandegames.slots.dafu.casino.BuildConfig;
import com.grandegames.slots.dafu.casino.R;
//import com.tiktok.TikTokBusinessSdk;

import org.cocos2dx.lib.Cocos2dxEventListener;
import org.cocos2dx.lib.Cocos2dxHelper;
import org.cocos2dx.plugin.PluginHelpshift;
import org.json.JSONObject;

/**
 * Created by bing on 08/01/2018.
 */

public class BoleApplication extends MultiDexApplication {

    private static final String TAG = "BoleApplication";

    @Override
    public void onCreate() {
        super.onCreate();

        if (Cocos2dxHelper.isLowSDStorage()){
            return;
        }

        // 火山应用性能监控
        BytesXMonitor.initMonitor(this);

        String appToken = getResources().getString(R.string.adjust_token);
        Log.d(TAG, "adjust_token="+appToken);
        String environment = AdjustConfig.ENVIRONMENT_PRODUCTION;
        AdjustConfig config = new AdjustConfig(this, appToken, environment);
        config.setLogLevel(LogLevel.VERBOSE);
        config.setEventDeduplicationIdsMaxSize(20);
        config.setOnDeferredDeeplinkResponseListener(new OnDeferredDeeplinkResponseListener() {
            @Override
            public boolean launchReceivedDeeplink(Uri deeplink) {
                Log.d("DeepLink", "deeplink log 0");
                //Toast.makeText(BoleApplication.this, "deferred deeplink uri="+deeplink.toString(), Toast.LENGTH_LONG).show();
                SDKManager.processDeeplinkData(deeplink);
                return true;
            }
        });
        config.setFbAppId("268692577001613");
        
        Adjust.initSdk(config);
        registerActivityLifecycleCallbacks(new AdjustLifecycleCallbacks());

        String apiKey = "fa789de6643ad973f38f4010692c2f30";
        String domain = "bolegames.helpshift.com";
        String appid = "bolegames_platform_20180226132107702-2c981dead95d951";
        PluginHelpshift.initHelpshift(this, apiKey, domain, appid);
//        String packageName = "com.grandegames.slots.dafu.casino";
//        String tictokAppid = "7307429699544711170";
//        TikTokBusinessSdk.TTConfig ttConfig = new TikTokBusinessSdk.TTConfig(getApplicationContext())
//                .setAppId(packageName)
//                .setTTAppId(tictokAppid)
//                .enableAutoIapTrack();
////        if(BuildConfig.DEBUG){
////            ttConfig.openDebugMode()
////                    .setLogLevel(TikTokBusinessSdk.LogLevel.DEBUG);
////        }
//        TikTokBusinessSdk.initializeSdk(ttConfig);
//        TikTokBusinessSdk.startTrack();

        Adjust.getAdid(new OnAdidReadListener() {
            @Override
            public void onAdidRead(String adid) {
                SDKManager.setAdjustADID(adid);
            }
        });

        Adjust.getAttribution(new OnAttributionReadListener() {
            @Override
            public void onAttributionRead(AdjustAttribution attribution) {
                if (null != attribution) {
                    JSONObject o = new JSONObject();
                    try {
                        o.put("network", attribution.network);
                    } catch (Exception e) {
                        Log.i("onAttributionRead", "exception attribution.network)");
                        e.printStackTrace();
                    }
                    try {
                        o.put("campaign", attribution.campaign);
                    } catch (Exception e) {
                        Log.i("onAttributionRead", "exception attribution.campaign");
                        e.printStackTrace();
                    }
                    try {
                        o.put("adgroup", attribution.adgroup);
                    } catch (Exception e) {
                        Log.i("onAttributionRead", "exception attribution.network)");
                        e.printStackTrace();
                    }
                    try {
                        o.put("creative", attribution.creative);
                    } catch (Exception e) {
                        Log.i("onAttributionRead", "exception attribution.campaign");
                        e.printStackTrace();
                    }

                    if (0 == o.length()) {
                        mAttribution = null;
                    } else {
                        Log.i("onAttributionRead", o.toString());
                        mAttribution = o.toString();
                    }

                } else {
                    mAttribution = null;
                }
            }
        });

    }

    private static final class AdjustLifecycleCallbacks implements ActivityLifecycleCallbacks {
        @Override
        public void onActivityCreated(Activity activity, Bundle bundle) {
        }

        @Override
        public void onActivityStarted(Activity activity) {
            SDKManager.lifeCycleOnStarted();
        }

        @Override
        public void onActivityResumed(Activity activity) {
//            Adjust.onResume();
        }

        @Override
        public void onActivityPaused(Activity activity) {
//            Adjust.onPause();
        }

        @Override
        public void onActivityStopped(Activity activity) {
            SDKManager.lifeCycleOnStopped();
        }

        @Override
        public void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
        }

        @Override
        public void onActivityDestroyed(Activity activity) {
        }
    }

    public static boolean isActivityVisible() {
        return activityVisible;
    }

    public static void activityResumed() {
        activityVisible = true;
    }

    public static void activityPaused() {
        activityVisible = false;
    }

    public static String getAttribution() {
        return mAttribution;
    }

    private static boolean activityVisible;
    private static String mAttribution;
}
