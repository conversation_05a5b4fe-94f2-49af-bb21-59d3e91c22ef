package org.cocos2dx.lua;

import static android.os.Looper.getMainLooper;

import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.os.Build;
import android.os.Looper;
import android.os.Message;
import android.os.MessageQueue;
import android.os.SystemClock;
import android.util.Log;
import android.util.Printer;

import org.cocos2dx.lib.Cocos2dxActivity;
import org.cocos2dx.lib.Cocos2dxHelper;
import org.cocos2dx.lib.Cocos2dxLuaJavaBridge;
import org.cocos2dx.lib.GLTaskMonitor;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 每300ms一条，总共记录100条信息
 * 消息聚合
 * 单独一条监控 还有一类消息也需要我们单独标记，以达到更好的识别，那就是可能会引起 ANR 的应用组件， 如 Activity，Service，Receiver，Provider 等等。为了监控这几种组件的执行过程，我们需要对 ActivityThread$H 的消息调度进行监控
 * IDLE 监控 在每次消息调度结束后，获取当前时间，在下次消息调度开始前，再次获取当前时间，并统计距离上次消息调度结束的间隔时长。如果间隔较长，那么也需要单独记录，如果间隔时间较短，我们认为可以忽略，并将其合并到之前统计的消息一起跟踪
 * 每条聚合消息信息包括：type, wall, cpu, tick, count, content
 *
 * 线程checkTime机制类
 *
 * ANR检测机制
 *
 * by sunyungao
 */
public class ANRMonitor {
    private final static String TAG = ANRMonitor.class.getSimpleName();
    private static final long FOREGROUND_MSG_THRESHOLD = -2000;    // 判定发生ANR时，如果应用在前台，下一条消息等待超时时长
    private static final long BACKGROUND_MSG_THRESHOLD = -10000;   // 判定发生ANR时，如果应用在后台，下一条消息等待超时时长
    private static final int CHECK_ERROR_STATE_INTERVAL = 500;     // 判定发生ANR时，监控进程NOT_RESPONDING状态间隔
    private static final int ANR_DUMP_MAX_TIME = 20000;            // 判定发生ANR时，监控进程NOT_RESPONDING状态的总时长

    private static final int POLY_MSG_MAX = 100;                   // 监控聚合消息 的数量最大值
    private static final int POLY_MSG_DELAY = 300;                 // 监控聚合消息 一条时间间隔 ms

    private boolean m_isSIGQUIT = false;                           // 是否收到 SIGQUIT 信号状态
    private long mStartWallTime;
    private long mEndWallTime;
    private String mCurMessageStr;
    private boolean mIsCheckTimeRun;

    private boolean mIsMessageFinished = true;                     // 是否 checkTime 检测的消息完成了
    private Thread mCheckTimeThread;                               // checkTime 检测线程
    private String mCheckTimeMainThreadStack;                      // checkTime 超时时主线程堆栈

    public ArrayList<String> mPastANRContent = new ArrayList<>();  // ANR 时UI主线程过去处理的消息数组
    public String mCurANRContent = "";                             // ANR 时UI主线程当前处理的消息数据
    public ArrayList<String> mPendANRContent = new ArrayList<>();  // ANR 时UI主线程未来处理的消息数组
    public String mCurGLStack = "";                                // ANR 时GLThread线程的堆栈信息

    private ConcurrentLinkedQueue<ANRPolyMessage> mPreMsgList = new ConcurrentLinkedQueue<>(); // 消耗过的聚合消息，最多100条

    private boolean mIsANROccur = false;

    void start() {
        startLooperMonitor();
        startThreadCheckTime();
    }

    /**
     * 停止监听
     * */
    void stop(){
        mIsCheckTimeRun = false;
        if (mCheckTimeThread != null){
            mCheckTimeThread.interrupt();
        }
        getMainLooper().setMessageLogging(null);
    }

    /**
     * 线程checkTime机制类
     * */
    void startThreadCheckTime(){
        mIsCheckTimeRun = true;
        mCheckTimeThread = new Thread(new Runnable() {
            @Override
            public void run() {
                while (mIsCheckTimeRun) {
                    try {
                        Thread.sleep(POLY_MSG_DELAY);
                        if (!mIsMessageFinished){
                            mCheckTimeMainThreadStack = getMainThreadStack();
                            //Log.d(TAG, "check time stack...\n" + mCheckTimeMainThreadStack);
                        }
                    } catch (InterruptedException e) {
                        mCheckTimeMainThreadStack = null;
                        // Log.d(TAG, "InterruptedException message restart check...");
                    }
                }
            }
        });
        mCheckTimeThread.start();
        mCheckTimeThread.setName("anrCheckTimeThread");
    }

    /**
     * 开始Looper中消息的监控
     * */
    private void startLooperMonitor(){
        getMainLooper().setMessageLogging(new Printer(){
            long startCPUTime, endCPUTime;
            long cpuDuration = 0;
            long wallDuration = 0;
            long totalWallDelay = 0;
            long totalCPUDuration = 0;
            int msgCount = 0;
            String preMsgContent = "";

            /**
             * 存储一条聚合消息
             * */
            void cachePolyMessageData(int p_type, long p_wall, long p_cpu, int p_count, String p_content, boolean hasStack){
                ANRPolyMessage apm = mPreMsgList.size() >= POLY_MSG_MAX ? mPreMsgList.poll() : new ANRPolyMessage();
                if (apm == null){
                    return;
                }
                apm.setData(p_type, p_wall, p_cpu, p_count, p_content);
                if (hasStack){
                    apm.stack = mCheckTimeMainThreadStack;
                }
                mPreMsgList.offer(apm);
            }

            void clearMonitorData(){
                totalWallDelay = 0;
                totalCPUDuration = 0;
                msgCount = 0;
                preMsgContent = "";
            }

            @Override
            public void println(String x) {
                if (m_isSIGQUIT){
                    return;
                }

                if (x.charAt(0) == '>'){ // x.contains(">>>>> Dispatching to")    ">>>>> Dispatching to " + msg.target + " " +  msg.callback + ": " + msg.what
                    mStartWallTime = SystemClock.elapsedRealtime();
                    startCPUTime = SystemClock.currentThreadTimeMillis();
                    mCurMessageStr = x;
                    //Log.d(TAG, x + "   time:" + mStartWallTime);

                    // idle状态持续时长
                    if (mEndWallTime > 0){
                        long idleDuration = mStartWallTime - mEndWallTime;
//                        Log.d(TAG, "idle duration:" + idleDuration);
                        if (idleDuration < POLY_MSG_DELAY){
                            msgCount++;
                            totalWallDelay += idleDuration;
                            preMsgContent = "idle";
                            if (totalWallDelay >= POLY_MSG_DELAY){ // 累计聚合
                                cachePolyMessageData(1, totalWallDelay, totalCPUDuration, msgCount, x, false);
                                clearMonitorData();
                            }
                        }else{
                            if (msgCount > 0){ // idle 超时，分开聚合
                                cachePolyMessageData(2, totalWallDelay, totalCPUDuration, msgCount, preMsgContent, false);
                            }
                            cachePolyMessageData(3, idleDuration, 0, 1, "idle", false);
                            clearMonitorData();
                        }
                    }

                    mIsMessageFinished = false;
                    mCheckTimeThread.interrupt();

                }else if (x.charAt(0) == '<'){ // x.contains("<<<<< Finished to")    "<<<<< Finished to " + msg.target + " " + msg.callback
                    mEndWallTime = SystemClock.elapsedRealtime();
                    endCPUTime = SystemClock.currentThreadTimeMillis();
                    //Log.d(TAG, x + "   time:" + mEndWallTime);

                    wallDuration = mEndWallTime - mStartWallTime;
                    cpuDuration = endCPUTime - startCPUTime;

//                    Log.d(TAG, "totalWallDelay:" + totalWallDelay + "  wallDuration:" + wallDuration);
                    if (wallDuration > POLY_MSG_DELAY || x.contains("ActivityThread$H")){
                        // 收集之前一条，当前一条数据
                        if (msgCount > 0){
                            cachePolyMessageData(4, totalWallDelay, totalCPUDuration, msgCount, preMsgContent, false);
                        }
                        cachePolyMessageData(5, wallDuration, cpuDuration, 1, x, true);
                        clearMonitorData();
                    }else{
                        msgCount++;
                        totalWallDelay += wallDuration;
                        totalCPUDuration += cpuDuration;
                        preMsgContent = x;

                        if (totalWallDelay >= POLY_MSG_DELAY){
                            cachePolyMessageData(6, totalWallDelay, totalCPUDuration, msgCount, x, false);
                            clearMonitorData();
                        }
                    }

                    mIsMessageFinished = true;
                }
            }
        });
    }

    /**
     * 打印当前消息聚合队列
     * */
    public void printPolyMessageData2(){
        Iterator<ANRPolyMessage> iterator = mPreMsgList.iterator();
        while (iterator.hasNext()) {
            ANRPolyMessage element = iterator.next();
            Log.d(TAG, "type:" + element.type + ", wall:" + element.wall + ", cpu:" + element.cpu + ", count:" + element.count + ", content:" + element.content + ", stack:" + element.stack);
        }
    }

    public void printPolyMessageData(){
        // 现在：当前正在执行的消息（idle、message）
        // 打印主线程堆栈
        ANRPolyMessage curANRPolyMessage = new ANRPolyMessage();
        if (mIsMessageFinished){ // idle 状态
            curANRPolyMessage.setData(7, (SystemClock.elapsedRealtime() - mEndWallTime), -1, 1, "idle");
        }else{ // message 状态，有消息正在处理
            curANRPolyMessage.setData(8, (SystemClock.elapsedRealtime() - mStartWallTime), -1, 1, mCurMessageStr);
        }
        curANRPolyMessage.stack = getMainThreadStack();

        // GLThread 堆栈信息
//        Thread glThread = ((Cocos2dxActivity) Cocos2dxHelper.getActivity()).getGLSurfaceView().getCocos2dxRendererThread();
//        if (glThread != null){
//            StackTraceElement[] st = glThread.getStackTrace();
//            StringBuilder sbf = new StringBuilder();
//            sbf.append("\n GlThread:\n");
//            for(StackTraceElement e : st){
//                sbf.append("      ").append(e.toString()).append("\n");
//            }
//            curANRPolyMessage.stack += sbf.toString();
//        }

        // 打印出来
        Log.d(TAG, "过去的聚合信息。。。");
        Iterator<ANRPolyMessage> iterator = mPreMsgList.iterator();
        while (iterator.hasNext()) {
            ANRPolyMessage element = iterator.next();
            Log.d(TAG, element.toPrintString());
        }

        Log.d(TAG, "当前的聚合信息。。。");
        Log.d(TAG, curANRPolyMessage.toPrintString());

        Log.d(TAG, "未来的信息。。。");
        Message mMessage = getMessageFromMessageQueue();
        while (mMessage != null){
            Log.d(TAG, "pending msg: " + messageToString(mMessage));
            mMessage = getMessageNext(mMessage);
        }
    }

    /**
     * 确认发生ANR，上报消息
     * */
    private void reportANR(){
        Log.d(TAG, "reportANR----");
    }

    /**
     * 获取主线程此时的堆栈
     * */
    private String getMainThreadStack(){
        StackTraceElement[] st = Looper.getMainLooper().getThread().getStackTrace();
        StringBuilder sbf =new StringBuilder();
        for(StackTraceElement e : st){
            sbf.append("      ").append(e.toString()).append("\n");
        }
//        Log.d(TAG, sbf.toString());
        return sbf.toString();
    }

    /**
     * 此时就应该预收集堆栈等消息了，否则真确定好了之后就来不及了
     * 在 SIGQUIT信号到来后收集信息，保留，等BUGLY确认ANR回调时传递该数据
     * */
    public void confirmRealAnr() {
        if (mIsANROccur){
            return;
        }
        mIsANROccur = true;
        Log.i(TAG, "confirmRealAnr begin");
        m_isSIGQUIT = true;

        // 发给lua执行打点等需求
        if(((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getGLSurfaceView().getCocos2dxRenderNativeInitCompleted()) {
            Cocos2dxHelper.runOnGLThread(new GLTaskMonitor(new Runnable() {
                @Override
                public void run() {
                    try {
                        Cocos2dxLuaJavaBridge.callLuaGlobalFunctionWithString("confirmRasterANR", "anr");
                    } catch (Exception e) {
                        Log.i("confirmRasterANR", e.toString());
                        e.printStackTrace();
                    }
                }
            }, "confirmRealAnr"));
        }

        mPastANRContent = new ArrayList<>();
        mCurANRContent  = "";
        mPendANRContent = new ArrayList<>();


        // 现在：当前正在执行的消息（idle、message）
        // 打印主线程堆栈
        ANRPolyMessage curANRPolyMessage = new ANRPolyMessage();
        if (mIsMessageFinished){ // idle 状态
            curANRPolyMessage.setData(7, (SystemClock.elapsedRealtime() - mEndWallTime), -1, 1, "idle");
        }else{ // message 状态，有消息正在处理
            curANRPolyMessage.setData(8, (SystemClock.elapsedRealtime() - mStartWallTime), -1, 1, mCurMessageStr);
        }
        curANRPolyMessage.stack = getMainThreadStack();

        // GLThread 堆栈信息
        Thread glThread = ((Cocos2dxActivity) Cocos2dxHelper.getActivity()).getGLSurfaceView().getCocos2dxRendererThread();
        if (glThread != null){
            StackTraceElement[] st = glThread.getStackTrace();
            StringBuilder sbf = new StringBuilder();
            sbf.append("\n GlThread:\n");
            for(StackTraceElement e : st){
                sbf.append("      ").append(e.toString()).append("\n");
            }
            curANRPolyMessage.stack += sbf.toString();
        }

        // 打印出来
        //Log.d(TAG, "过去的聚合信息。。。");
        Iterator<ANRPolyMessage> iterator = mPreMsgList.iterator();
        while (iterator.hasNext()) {
            ANRPolyMessage element = iterator.next();
            //Log.d(TAG, element.toPrintString());
            mPastANRContent.add(element.toPrintString());
        }

        //Log.d(TAG, "当前的聚合信息。。。");
        mCurANRContent = curANRPolyMessage.toPrintString();
        //Log.d(TAG, curANRPolyMessage.toPrintString());

        //Log.d(TAG, "未来的信息。。。");
        Message mMessage = getMessageFromMessageQueue();
        while (mMessage != null){
            //Log.d(TAG, "pending msg: " + mMessage.toString());
            mPendANRContent.add(messageToString(mMessage));
            mMessage = getMessageNext(mMessage);
        }

        m_isSIGQUIT = false;

        // 后续判断是否是真的ANR
//        boolean needReport = isMainThreadBlocked();
//        if (needReport) {
//            reportANR();
//        } else {
//            new Thread(new Runnable() {
//                @Override
//                public void run() {
//                    checkErrorStateCycle();
//                }
//            }, "Check-ANR-State-Thread").start();
//        }
    }

    /**
     * 获取Message的数据字符串
     * */
    private String messageToString(Message message){
        StringBuilder b = new StringBuilder();
        b.append("{ when=");
        b.append(message.getWhen() - SystemClock.uptimeMillis());

        if (message.getTarget() != null) {
            if (message.getCallback() != null) {
                b.append(" callback=");
                b.append(message.getCallback().getClass().getName());
            } else {
                b.append(" what=");
                b.append(message.what);
            }

            if (message.arg1 != 0) {
                b.append(" arg1=");
                b.append(message.arg1);
            }

            if (message.arg2 != 0) {
                b.append(" arg2=");
                b.append(message.arg2);
            }

            if (message.obj != null) {
                b.append(" obj=");
                b.append(message.obj);
            }

            b.append(" target=");
            if (message.getTarget() != null){
                try {
                    b.append(message.getTarget().getClass().getName());
                }catch (Exception exp){
                    b.append("null");
                }
            } else {
                b.append("null");
            }
        } else {
            b.append(" barrier=");
            b.append(message.arg1);
        }

        b.append(" }");
        return b.toString();
    }

    /**
     * 获取主线程 MessageQueue 中的 mMessages
     * */
    private Message getMessageFromMessageQueue(){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){
            try {
                MessageQueue mainQueue = Looper.getMainLooper().getQueue();
                Field field = mainQueue.getClass().getDeclaredField("mMessages");
                field.setAccessible(true);
                final Message mMessage = (Message) field.get(mainQueue);
                return mMessage;
            } catch (Exception e) {
                return null;
            }
        }else{
            return null;
        }
    }

    /**
     * 获取消息链表的下一条消息
     * */
    private Message getMessageNext(Message msg){
        if (msg == null){
            return null;
        }

        try {
            Field field = msg.getClass().getDeclaredField("next");
            field.setAccessible(true);
            final Message mMessage = (Message) field.get(msg);
            return mMessage;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 如果下一条消息超时一定时长，再加上之前判定的sigquit信号，则判定为发生ANR
     * */
    private boolean isMainThreadBlocked() {
        try {
            final Message mMessage = getMessageFromMessageQueue();
            if (mMessage != null) {
                long when = mMessage.getWhen();
                if (when == 0) {
//                    Log.i(TAG, "mMessage time = 0");
                    return false;
                }
                long time = when - SystemClock.uptimeMillis();
                long timeThreshold = BACKGROUND_MSG_THRESHOLD;
                if (Cocos2dxHelper.isActivityVisible()) {
                    timeThreshold = FOREGROUND_MSG_THRESHOLD;
                }
//                Log.i(TAG, "mMessage time:" + time + "  timeThreshold:" + timeThreshold);
                return time < timeThreshold;
            } else {
                Log.i(TAG, "mMessage is null");
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * 每隔500ms检测一次 NOT_RESPONDING 标签，一共持续20s
     * */
    private void checkErrorStateCycle() {
        int checkErrorStateCount = 0;
        int lastCount = ANR_DUMP_MAX_TIME / CHECK_ERROR_STATE_INTERVAL;
        while (checkErrorStateCount < lastCount) {
            try {
                checkErrorStateCount++;
                boolean myAnr = checkErrorState();
                if (myAnr) {
                    reportANR();
                    break;
                }

                Thread.sleep(CHECK_ERROR_STATE_INTERVAL);
            } catch (Throwable t) {
                Log.e(TAG, "checkErrorStateCycle error, e : " + t.getMessage());
                break;
            }
        }
    }

    /**
     * 检测主线程是否有 NOT_RESPONDING 状态
     * */
    private boolean checkErrorState() {
        try {
//            Log.i(TAG, "[checkErrorState] start");
            Application application = Cocos2dxHelper.getActivity().getApplication();
            ActivityManager am = (ActivityManager) application.getSystemService(Context.ACTIVITY_SERVICE);

            List<ActivityManager.ProcessErrorStateInfo> procs = am.getProcessesInErrorState();
            if (procs == null) {
//                Log.i(TAG, "[checkErrorState] procs == null");
                return false;
            }

            for (ActivityManager.ProcessErrorStateInfo proc : procs) {
//                Log.i(TAG, "[checkErrorState] found Error State proccessName = " + proc.processName + ", proc.condition = " + proc.condition);

                if (proc.uid != android.os.Process.myUid() && proc.condition == ActivityManager.ProcessErrorStateInfo.NOT_RESPONDING) {
//                    Log.i(TAG, "maybe received other apps ANR signal");
                    return false;
                }

                if (proc.pid != android.os.Process.myPid()) continue;

                if (proc.condition != ActivityManager.ProcessErrorStateInfo.NOT_RESPONDING) {
                    continue;
                }

                Log.i(TAG, "error sate longMsg = " + proc.longMsg); // 会打印出CPU信息

                return true;
            }
            return false;
        } catch (Throwable t) {
            Log.e(TAG, "[checkErrorState] error : " + t.getMessage());
        }
        return false;
    }

    /**
     * 每条聚合消息的消息类
     */
    private static class ANRPolyMessage {
        public int type;
        public long wall;
        public long cpu;
        public long tick; // 当前时间戳
        public int count;
        public String content;
        public String stack = null;

        void setData(int p_type, long p_wall, long p_cpu, int p_count, String p_content){
            type = p_type;
            wall = p_wall;
            cpu = p_cpu;
            count = p_count;
            content = p_content;
            tick = System.currentTimeMillis();
            stack = null;
        }

        String toPrintString(){
            String stackStr = "";
            if (stack != null){
                stackStr = ",stack:\n" + stack;
            }
            return "type:" + type + ",wall:" + wall + ",cpu:" + cpu + ",tick:" + tick + ",count:" + count + ",content:" + content + stackStr;
        }
    }
}

