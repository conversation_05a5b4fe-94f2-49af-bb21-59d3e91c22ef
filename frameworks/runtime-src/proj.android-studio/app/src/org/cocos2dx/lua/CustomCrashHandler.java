package org.cocos2dx.lua;
/*
import android.util.Log;

import com.grandegames.slots.dafu.casino.BuildConfig;
import com.tencent.bugly.crashreport.CrashReport;

import org.cocos2dx.lib.Cocos2dxActivity;
import org.cocos2dx.lib.Cocos2dxHelper;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;

public class CustomCrashHandler extends CrashReport.CrashHandleCallback {
    private static final String TAG = "CustomCrashHandler";
    @Override
    public synchronized Map<String, String> onCrashHandleStart(int crashType, String errorType, String errorMessage, String errorStack) {
        Log.d(TAG, "onCrashHandleStart type:" + crashType + "  :" + errorType + "  :" + errorMessage + "  :" + errorStack);
        Map<String, String> ret = new HashMap<>();
        ret.put("build_type", BuildConfig.BUILD_TYPE);

        if (Cocos2dxHelper.getIsLibLoadSuccess()){
            ret.put("collected_msg", getCollectedMsg());
            ret.put("onDrawFrameLast", String.valueOf((System.nanoTime() - tickTime) / 1000000000L));
            Log.d(TAG, "onDrawFrameLast=" + ret.get("onDrawFrameLast"));
        }

        // 崩溃时存储，再打开时lua获取崩溃时广告主是谁
        if (Cocos2dxHelper.getIsInited() && Cocos2dxHelper.getActivity() != null){
            Cocos2dxHelper.setMMKVIntegerForKey("crash240815", crashType);
            Cocos2dxHelper.setMMKVStringForKey("crash240815_ads", CrashReport.getUserData(Cocos2dxHelper.getActivity(), "ads"));
        }


        ret.put("ADRewardLoading", String.valueOf(MemoryMonitor.isADRewardLoading));
        ret.put("ADRewardPlaying", String.valueOf(MemoryMonitor.isADRewardPlaying));
        ret.put("ADInterLoading", String.valueOf(MemoryMonitor.isADInterLoading));
        ret.put("ADInterPlaying", String.valueOf(MemoryMonitor.isADInterPlaying));
        ret.put("isEnterForeground", String.valueOf(BoleApplication.isActivityVisible()));

        Log.d(TAG, "collected_msg=" + ret.get("collected_msg"));
        Log.d(TAG, "errorStack=" + errorStack);

        // 内存统计
        JSONObject jsonObj = MemoryMonitor.statisticsMemory();
        Iterator<String> keys = jsonObj.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = null;
            try {
                value = jsonObj.get(key);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            ret.put(key, String.valueOf(value));
            Log.d(TAG, key + "=" + value);
        }

        // 内存计数
        ArrayList<String> infoArray = MemoryMonitor.getCrashInfoList();
        for (int i = 0; i < infoArray.size(); i++){
            ret.put(String.valueOf(i), infoArray.get(i));
        }

        // 存储到本地指定文件内
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        String currentTime = sdf.format(new Date());
        String msgStr = crashType + "\n" + currentTime + "\n" + errorStack;
        MemoryMonitor.cacheCrashOrANRData(msgStr);
        MemoryMonitor.cacheMemoryInfo(infoArray);

        return ret;
    }

    @Override
    public synchronized byte[] onCrashHandleStart2GetExtraDatas(int crashType, String errorType, String errorMessage, String errorStack) {
        // Log.d(TAG, "onCrashHandleStart2GetExtraDatas type: " + crashType + "  \n:" + errorType + "  \n:" + errorMessage + "  \n:" + errorStack);
        try {
            if (crashType == CRASHTYPE_ANR){
                StringBuilder data = new StringBuilder("curr   " + ((Cocos2dxActivity) Cocos2dxHelper.getActivity()).getANRCurString());
                ArrayList<String> pastANRList = ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getANRPastString();
                for (int i = 0; i < pastANRList.size(); i++){
                    data.append("\npast ").append(i + 1).append("   :").append(pastANRList.get(i));
                }

                data.append("\n");

                ArrayList<String> pendANRList = ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getANRPendString();
                for (int i = 0; i < pendANRList.size(); i++){
                    data.append("\npend ").append(i + 1).append("   :").append(pendANRList.get(i));
                }
                return data.toString().getBytes("UTF-8");
            }else{
                return null;
            }
        } catch (Exception e) {
            return null;
        }
//        return super.onCrashHandleStart2GetExtraDatas(crashType, errorType, errorMessage, errorStack);
    }

    private static native String getCollectedMsg();
}
 */
