/****************************************************************************
Copyright (c) 2008-2010 Ricardo <PERSON>
Copyright (c) 2010-2012 cocos2d-x.org
Copyright (c) 2011      Zynga Inc.
Copyright (c) 2013-2014 Chukong Technologies Inc.
 
http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
****************************************************************************/
package org.cocos2dx.lua;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.MessageQueue;
import android.os.PowerManager;
import android.provider.Settings;
import androidx.core.app.ActivityCompat;

import android.text.TextUtils;
import android.util.Log;
import com.facebook.FacebookSdk;
import com.facebook.appevents.AppEventsLogger;
import com.facebook.applinks.AppLinkData;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKManager;
import com.grandegames.slots.dafu.casino.R;

import org.cocos2dx.bole.notification.BoleNotification;
import org.cocos2dx.fcm.FCMRegistrar;
import org.cocos2dx.lib.Cocos2dxActivity;
import org.cocos2dx.lib.Cocos2dxEventListener;
import org.cocos2dx.lib.Cocos2dxGLSurfaceView;
import org.cocos2dx.lib.Cocos2dxLuaJavaBridge;
import org.cocos2dx.lib.GLTaskMonitor;
import org.cocos2dx.plugin.FacebookWrapper;
import org.cocos2dx.plugin.FirebaseWrapper;
import org.cocos2dx.plugin.PluginEventListener;
import org.cocos2dx.plugin.PluginWrapper;
import org.cocos2dx.bole.amazon.iap.AmazonIapManager;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.Iterator;
import android.content.ComponentCallbacks2;
import android.widget.Toast;

import org.cocos2dx.lib.Cocos2dxHelper;
import android.os.Looper;

public class AppActivity extends Cocos2dxActivity implements ComponentCallbacks2{
	public static String ANDROID = "ANDROID";
	public static String AMAZON = "AMAZON";
	public String PLATFORM = ANDROID;

	public static String urlParameter="";
	private static AppActivity Instance;
	PowerManager.WakeLock mWakeLock = null;
	private Cocos2dxGLSurfaceView glSurfaceView = null;
	private final static String TAG = AppActivity.class.getSimpleName();
//	private FirebaseAnalytics mFirebaseAnalytics;
	public static  Boolean isFront = false;

	private ANRMonitor mAnrMonitor;

	private static final int REQUEST_CODE = 0;

	static final String[] PERMISSIONS = new String[]{
			Manifest.permission.READ_PHONE_STATE,
			Manifest.permission.GET_ACCOUNTS,
			Manifest.permission.READ_LOGS,
			Manifest.permission.INSTALL_PACKAGES
	};
//	private boolean isRequireCheck;
	public static String tipTitle = "Advice";
	public static String tipContent = "Set permissions for better experiences";
	public static String tipConfirm = "OK";
	public static String tipCancel = "Cancel";
	private static boolean gameLaunchFinished = false;
	public static int mLaunchFlag = -1;

	private EventListener listener = new EventListener() {
		@Override
		public void loadSyncThreadAtStartApp(String key) {
			Cocos2dxHelper.loadSyncThreadAtStartApp(key);
		}
		@Override
		public void onLogin(int sdkType, int errorCode, String id, String name, String token, String icon, String email, final JSONObject json, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject result = json;
						if(result == null) {
							result = new JSONObject();
						}
						try {
							result.put("sdkType", sdkType);
							result.put("code", errorCode);
							if(!TextUtils.isEmpty(id)) {
								result.put("id", id);
							}
							if(!TextUtils.isEmpty(name)) {
								result.put("name", name);
							}
							if(!TextUtils.isEmpty(token)) {
								result.put("token", token);
							}
							if(!TextUtils.isEmpty(icon)) {
								result.put("icon", icon);
							}
							if(!TextUtils.isEmpty(email)) {
								result.put("email", email);
							}
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, result.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onLogin"));
			}
		}

		@Override
		public void onLogout(int sdkType, int errorCode, JSONObject json, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject result = json;
						if(result == null) {
							result = new JSONObject();
						}
						try {
							result.put("sdkType", sdkType);
							result.put("code", errorCode);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, result.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onLogout_"+errorCode));
			}
		}

		@Override
		public void onPurchase(int sdkType, int errorCode, String productId, String orderId, String payload, String token, JSONObject jsonData, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = jsonData;
						if(json == null) {
							json = new JSONObject();
						}
						try {
							json.put("sdkType", sdkType);
							json.put("code", errorCode);
							json.put("product_id", productId);
							json.put("order_id", orderId);
							if(!TextUtils.isEmpty(payload)) {
								JSONObject payloadData = new JSONObject(payload);
								Iterator<String> keys = payloadData.keys();
								while(keys.hasNext()) {
									String key = keys.next();
									json.put(key, payloadData.get(key));
								}
							}
							json.put("purchase_token", token);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Log.d(TAG, "onPurchase origin string = " + json.toString());
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onPurchase"));
			}
		}

		@Override
		public void onUndeliveredOrder(int sdkType, int errorCode, String productId, String orderId, String payload, String token, JSONObject jsonData, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = jsonData;
						if(json == null) {
							json = new JSONObject();
						}
						try {
							json.put("sdkType", sdkType);
							json.put("code", errorCode);
							json.put("product_id", productId);
							json.put("order_id", orderId);
							if(!TextUtils.isEmpty(payload)) {
								JSONObject payloadData = new JSONObject(payload);
								Iterator<String> keys = payloadData.keys();
								while(keys.hasNext()) {
									String key = keys.next();
									json.put(key, payloadData.get(key));
								}
							}
							json.put("purchase_token", token);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Log.d(TAG, "onPurchase origin string = " + json.toString());
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
					}
				}, "AppActivity_onUndeliveredOrder"));
			}
		}

		@Override
		public void onQuerySkuDetails(final int sdkType, final int errorCode, final JSONArray data, final int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = new JSONObject();
						try {
							json.put("sdkType", sdkType);
							json.put("code", errorCode);
							if(data != null) {
								json.put("sku_details", data);
							}
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback,json.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onQuerySkuDetails_"+errorCode));
			}
		}

		@Override
		public void onGetNotificationSound(String sound, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = new JSONObject();
						try {
							json.put("notification_sound", sound);
							Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback,json.toString());
							Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
						} catch (JSONException e) {
							throw new RuntimeException(e);
						}
					}
				}, "AppActivity_onGetNotificationSound"));
			}
		}

		@Override
		public void onGameLaunchStatus(boolean status) {
			gameLaunchFinished = status;
		}

		@Override
		public void onGetStorageStats(long cachedBytes, long dataBytes, long appBytes, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = new JSONObject();
						try {
							json.put("cachedBytes", cachedBytes);
							json.put("dataBytes", dataBytes);
							json.put("appBytes", appBytes);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onGetStorageStats"));
			}
		}

		@Override
		public void onCheckNotificationPermission(boolean enabled, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = new JSONObject();
						try {
							json.put("result", enabled ? 2 : 1);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onCheckNotificationPermission"));
			}
		}

		@Override
		public void onCheckNotificationPermission2(boolean enabled, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = new JSONObject();
						try {
							json.put("result", enabled ? 2 : 1);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onCheckNotificationPermission2"));
			}
		}

		@Override
		public void onRequestNotificationPermission(boolean enabled, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = new JSONObject();
						try {
							json.put("result", enabled);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onRequestNotificationPermission"));
			}
		}

		@Override
		public void onCreateShortCut(boolean enabled, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = new JSONObject();
						try {
							json.put("result", enabled);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onCreateShortCut"));
			}
		}

		@Override
		public void onAdEvent(int sdkType, int eventCode, JSONObject data, int luaCallback) {
			MemoryMonitor.updateADState(eventCode);
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = data;
						if(json == null) {
							json = new JSONObject();
						}
						try {
							json.put("sdkType", sdkType);
							json.put("code", eventCode);
							Log.d(TAG, "onAdEvent: " + json.toString());
							Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						} catch (JSONException e) {
							e.printStackTrace();
						}
					}
				}, "AppActivity_onAdEvent_"+eventCode));
			}
		}
		@Override
		public void onAudioAdEvent(int sdkType, int eventCode, JSONObject data, int luaCallback) {
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = data;
						if(json == null) {
							json = new JSONObject();
						}
						try {
							json.put("sdkType", sdkType);
							json.put("code", eventCode);
							Log.d(TAG, "onAdEvent: " + json.toString());
							Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						} catch (JSONException e) {
							e.printStackTrace();
						}
					}
				}, "AppActivity_onAudioAdEvent_"+eventCode));
			}
		}

		@Override
		public void onAddAPMTag(String key, String value) {
			BytesXMonitor.addTags(key, value);
		}

		@Override
		public void onRemoveAPMTag(String key) {
			BytesXMonitor.removeTags(key);
		}

		@Override
		public void onSetUserIdToAPM(String userId) {
			BytesXMonitor.setUserId(userId);
		}

		@Override
		public void onShareEvent(boolean enabled, String msg, int luaCallback){
			if(luaCallback != -1) {
				AppActivity.this.runOnGLThread(new GLTaskMonitor(new Runnable() {
					@Override
					public void run() {
						JSONObject json = new JSONObject();
						try {
							json.put("result", enabled);
							json.put("msg", msg);
						} catch (JSONException e) {
							e.printStackTrace();
						}
						Cocos2dxLuaJavaBridge.callLuaFunctionWithString(luaCallback, json.toString());
						Cocos2dxLuaJavaBridge.releaseLuaFunction(luaCallback);
					}
				}, "AppActivity_onShareEvent_"+msg));
			}
		}
	};

//	private long curTime = System.currentTimeMillis();
	private final Cocos2dxEventListener cocosListener = new Cocos2dxEventListener() {
		@Override
		public void addAPMTags(String key, String value) {
			BytesXMonitor.addTags(key, value);
			BytesXMonitor.setVLogI(key, value);
//			long curr = System.currentTimeMillis();
//			value = value + "  :" + (curr - curTime);
//			Log.d(key, value);
//			curTime = curr;
		}
		@Override
		public void loadSyncThreadAtResetFinished() {
			if (Instance != null) {
				Instance.afterLoadNativeLibs();
			}
		}
	};

	private final PluginEventListener pluginEventListener = new PluginEventListener() {
		@Override
		public void pluginRunOnGLThread(Runnable r, String tag) {
			runOnGLThread(new GLTaskMonitor(r, tag));
		}
	};

	private void addAPMTags(String key, String value) {
		cocosListener.addAPMTags(key, value);
	}

	@Override
	protected void afterLoadNativeLibs() {
		super.afterLoadNativeLibs();

		PluginWrapper.setGLSurfaceView(this.mGLSurfaceView);
		Log.d("DeepLink", "deeplink log 1");
		setLaunchPath(1);
		processDeepLinkData(getIntent());
//		isRequireCheck = true;

		// ANR监听、收集器
//		if (mAnrMonitor == null){
//			mAnrMonitor = new ANRMonitor();
//			mAnrMonitor.start();
//			Cocos2dxHelper.startWatchANR();
//		}

	}

	@Override
	public Cocos2dxGLSurfaceView onCreateView() {
		Log.i("Bing",TAG + " :onCreateView");
		glSurfaceView =  new Cocos2dxGLSurfaceView(this); //super.onCreateView(); //
		this.hideSystemUI();
		// TestCpp should create stencil buffer
		glSurfaceView.setEGLConfigChooser(5, 6, 5, 0, 16, 8);
		try {
			ApplicationInfo ai = getPackageManager().getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
			Bundle bundle = ai.metaData;
			PLATFORM = bundle.getString("android.app.platform");
		} catch(Exception e) {}
		Log.i("Bing", "PLATFORM = "+PLATFORM);
		if (AMAZON.equals(PLATFORM)) {
			AmazonIapManager.setupIAPOnCreate(this);
		}
		if (mWakeLock == null) {
			PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
			mWakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK, "XYTEST");
			mWakeLock.acquire();
		}
		return glSurfaceView;
	}

	void initPlugins(){
		PluginWrapper.init(this);
		FacebookWrapper.initActivity(this);
		FacebookWrapperInitPlugins();
		FirebaseWrapper.onCreate(this);
		FCMRegistrarInitPlugins();
	}

	private void FCMRegistrarInitPlugins() {
		// MARK:8ms 没必要在这里拿，写这个目的是在lua里能拿到fcm的令牌
		Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
			@Override
			public boolean queueIdle() {
				FCMRegistrar.getInstance().init(Instance);
				Cocos2dxHelper.loadSyncThreadAtStartApp("FCMRegistrar");
				return false;
			}
		});
	}

	private void FacebookWrapperInitPlugins() {
		// MARK: 38 tobe solved, deeplink 等待等方法。担心还有其他地方调用（问下其他人有清楚的没）
		Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
			@Override
			public boolean queueIdle() {
				FacebookWrapper.createIfNeeded();
				Cocos2dxHelper.loadSyncThreadAtStartApp("FacebookWrapper");
				return false;
			}
		});
	}

	// 节省 12+16+17=45+65=110+12=122+8=130+38=168+77=245ms
	// 删除 cocosMusic，cocosSound，cocosOBB，
	// 异步 R.array.account_sdks，createNotificationChannel（如果要去掉，需要在lua里添加一次）,
	// FCMRegistrarInitPlugins,FacebookWrapperInitPlugins，hideVirtualButton,
	@Override
	protected void onCreate(Bundle savedInstanceState) {
		// TODO Auto-generated method stub
		Instance = this;
		Cocos2dxHelper.setListener(cocosListener);
		Cocos2dxHelper.setLoadSyncThreadFinished(false);
		PluginWrapper.setListener(pluginEventListener);
		super.onCreate(savedInstanceState);
		initPlugins();
//		SDKManager.init(this, R.array.account_pay_sdks, R.array.pay_sdks, R.array.account_sdks, R.array.ad_sdks, R.array.more_sdks, listener);
		SDKManager.init(this, R.array.account_pay_sdks, R.array.pay_sdks, R.array.account_sdks, R.array.ad_sdks, R.array.more_sdks, R.array.audio_sdks, listener);
		BoleNotification.clearNotifications();
	}

	public void googleCMPStart(){
		GoogleMobileAdsConsentManager mGoogleUMPManager = GoogleMobileAdsConsentManager.getInstance(getApplicationContext());
		mGoogleUMPManager.gatherConsent(
				this,
				consentError -> {
					if (consentError != null) {
						// Consent not obtained in current session.
						Log.w(TAG, String.format("%s: %s", consentError.getErrorCode(), consentError.getMessage()));
					}
				});
	}

	public void googleCMPOptions(){
		GoogleMobileAdsConsentManager.getInstance(getApplicationContext()).showPrivacyOptionsForm(
				this,
				formError -> {
					if (formError != null) {
						Toast.makeText(this, formError.getMessage(), Toast.LENGTH_SHORT).show();
					}
				});
	}

	public boolean googleCMPCanRequestAds(){
		return GoogleMobileAdsConsentManager.getInstance(getApplicationContext()).canRequestAds();
	}

	public boolean isGoogleCMPPrivacyOptionsRequired(){
		return GoogleMobileAdsConsentManager.getInstance(getApplicationContext()).isPrivacyOptionsRequired();
	}

	public void confirmANR(){
		if (mAnrMonitor != null) {
			mAnrMonitor.confirmRealAnr();
		}
	}

	public void stopANRMonitor(){
		if (mAnrMonitor != null) {
			mAnrMonitor.stop();
		}
	}

	public void printRasterMessage() {
		if (mAnrMonitor != null) {
			mAnrMonitor.printPolyMessageData();
		}
	}

	public ArrayList<String> getANRPastString() {
		if (mAnrMonitor != null) {
			return mAnrMonitor.mPastANRContent;
		}else{
			return new ArrayList<>();
		}
	}
	public String getANRCurString() {
		if (mAnrMonitor != null) {
			return mAnrMonitor.mCurANRContent;
		}else{
			return "";
		}
	}
	public ArrayList<String> getANRPendString() {
		if (mAnrMonitor != null) {
			return mAnrMonitor.mPendANRContent;
		}else{
			return new ArrayList<>();
		}
	}

	@Override
	protected void onNewIntent(Intent intent) {
		super.onNewIntent(intent);
		Log.d("scene DeepLink", "deeplink log 2");
		setLaunchPath(1);
		processDeepLinkData(intent);
		SDKManager.onNewIntent(intent);
	}

	private void processDeepLinkData(Intent intent)
	{
		Log.d("scene DeepLink", "deeplink log 3");
		FacebookWrapper.createIfNeeded();
		AppLinkData.fetchDeferredAppLinkData(this,
				new AppLinkData.CompletionHandler() {
					@Override
					public void onDeferredAppLinkDataFetched(AppLinkData appLinkData) {
						// Process app link data
						Log.d("scene DeepLink", "deeplink log 4");
						if (appLinkData != null) // 非deferred时appLinkData==null
						{
							Log.d("DeepLink", "deeplink log 5");
							Uri uri = appLinkData.getTargetUri();
							if (uri != null)
							{
								Log.d("DeepLink", "deeplink log 6");
								Log.d(TAG, "ppp 000 " + uri);
								String parameter = uri.getQueryParameter("parameter");
								if (parameter != null)
								{
									Log.d("DeepLink", "deeplink log 7");
									Log.d(TAG, "ppp 001 " + parameter);
									urlParameter = parameter;
									Log.d(TAG, "deferred deep link parameter: " + parameter);
								}
								SDKManager.processDeeplinkData(uri);
							}
						} else {
							Log.d("DeepLink", "deeplink log 8");
							Log.d("DeepLink", "appLinkData is null");
						}
					}
				}
		);
		if (intent != null)
		{
			Log.d("DeepLink", "deeplink log 9");
			Uri uri = intent.getData();
			if (uri != null)
			{
				setLaunchPath(2);
				Log.d("DeepLink", "deeplink log 10");
				String parameter = uri.getQueryParameter("parameter");
				if (parameter != null)
				{
					urlParameter = parameter;
					Log.d("DeepLink", "deeplink log 11");
				}
				SDKManager.processDeeplinkData(uri);
			}else{
				int jwLaunch = intent.getIntExtra("jwLaunch", -1);
				if(jwLaunch != -1){
					setLaunchPath(jwLaunch);
				}
			}

			Log.d("DeepLink", "deeplink log 12");
			Bundle pushData = intent.getBundleExtra("push");
			if (pushData != null) {
				Log.d("DeepLink", "deeplink log 13");
				final AppEventsLogger logger = AppEventsLogger.newLogger(this);
				logger.logPushNotificationOpen(pushData, intent.getAction());
			}
			Log.d("DeepLink", "deeplink log 14");
		}
		Log.d("DeepLink", "deeplink log 15");
	}

	public static String getUrlParameter()
	{
		Log.d("DeepLink", "getUrlParameter = " + urlParameter);
		String deeplinkUrl = urlParameter;
		urlParameter = "";
		return deeplinkUrl;
	}

	public static void changedActivityOrientation(int orientation)
	{
		switch(orientation)
		{
			case 1://横屏
				Instance.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE);
				break;
			case 2://竖屏
				Instance.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT);
				break;
			default:
				break;
		}
	}

	public static void forceUpdateApp(String url, String url_backup)
	{
		Instance.goToStore(url, url_backup);
	}

	public void goToStore(String url, String url_backup)
	{
		try
		{
			startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(url)));
		}
		catch (android.content.ActivityNotFoundException anfe)
		{
			startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(url_backup)));
		}
	}

	public static void showPermissionTips(String title, String content, String confirm, String cancel)
	{
		tipTitle = title;
		tipContent = content;
		tipConfirm = confirm;
		tipCancel = cancel;
	}

	public void setFront(Boolean bFront){

		Log.d(TAG,"setFront" + (bFront?"1":"0"));
		isFront = bFront;
	}

	@Override
	protected void onResume() {
		super.onResume();
//		if (!Cocos2dxHelper.isOnCreateCanGo()){
//			return;
//		}
		PluginWrapper.onResume();
		if(mWakeLock == null)
		{   // 屏幕常亮
			PowerManager pm = (PowerManager)getSystemService(Context.POWER_SERVICE);
			mWakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK, "XYTEST");
			mWakeLock.acquire();
		}

		BoleApplication.activityResumed();
		if (AMAZON.equals(PLATFORM)) {
			AmazonIapManager.onResume();
		}

//		if (isRequireCheck) {
//			if (lacksPermissions()) {
//			}
//		}
//		else {
//			isRequireCheck = true;
//		}
		SDKManager.onResume();
	}

	@Override
	protected void onPause() {
		super.onPause();
		PluginWrapper.onPause();

		if(mWakeLock != null)
		{
			mWakeLock.release();
			mWakeLock = null;
		}

		BoleApplication.activityPaused();

//		if (!Cocos2dxHelper.isOnCreateCanGo()){
//			return;
//		}

		SDKManager.onPause();
	}

	@Override
	public void onDestroy()
	{
		super.onDestroy();
		if(mWakeLock  != null) {
			mWakeLock.release();
			mWakeLock = null;
		}
		SDKManager.onDestroy();
	}

	private boolean lacksPermissions() {
		for (String permission : PERMISSIONS) {
			if (ActivityCompat.checkSelfPermission(this, permission)!= PackageManager.PERMISSION_GRANTED) {
				return true;
			}
		}
		return false;
	}

	private boolean hasAllPermissionsGranted(int[] grantResults) {
		for (int grantResult : grantResults) {
			if (grantResult == PackageManager.PERMISSION_DENIED) {
				return false;
			}
		}
		return true;
	}

	@Override
	public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
		SDKManager.onRequestPermissionsResult(requestCode, permissions, grantResults);
		super.onRequestPermissionsResult(requestCode, permissions, grantResults);
		if (requestCode == 1) { // 保存相册
			// 检查权限请求的结果
			if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
				// 如果权限被授予，执行需要访问存储的操作
				Cocos2dxHelper.saveImageToGallery(Cocos2dxHelper.saveImageToGalleryPath);
			} else {
				// 如果权限被拒绝，可以给用户提示或者禁用相关功能
				// Toast.makeText(getApplicationContext(), "saved image fail.", Toast.LENGTH_SHORT).show();
				Cocos2dxHelper.callLuaGlobalFunctionWithStringOnGL("androidRequestPermissionsResult", "fail");
			}
		}
	}

	public void showMissingPermissionDialog() {
		AlertDialog.Builder builder = new AlertDialog.Builder(this);
		builder.setTitle(tipTitle);
		builder.setMessage(tipContent);
		builder.setNegativeButton(tipCancel, null);
		builder.setPositiveButton(tipConfirm, new DialogInterface.OnClickListener() {
			@Override
			public void onClick(DialogInterface dialog, int which) {
				startAppSettings();
			}
		});
		builder.create().show();
	}

	private void startAppSettings() {
		Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
		intent.setData(Uri.parse("package:" + getPackageName()));
		startActivity(intent);
	}

	@Override
	protected void onActivityResult(int requestCode, int resultCode, Intent data) {

		Log.i("Bing","AppActivity onActivityResult");
		if(!PluginWrapper.onActivityResult(requestCode, resultCode, data))
		{
			super.onActivityResult(requestCode, resultCode, data);
		}
		FacebookWrapper.onActivityResult(requestCode, resultCode, data, this);

		SDKManager.onActivityResult(requestCode, resultCode, data);
	}

	@Override
	public void onSaveInstanceState(Bundle outState) {
		super.onSaveInstanceState(outState);
		// FacebookWrapper.onSaveInstanceState(outState);
	}

	@Override
	// TODO Mopub Close Ads
	public void onBackPressed() {
		// If an interstitial is on screen, close it.
		boolean chart = false;
//		try {
//			Class<?> c = Class.forName("com.chartboost.sdk.Chartboost");
//			Method method = c.getDeclaredMethod("onBackPressed");
//			chart = (Boolean) method.invoke(c);
//		} catch(Exception e) {
////        	Log.i(tag, "failed to reflection adjust referrer:"+e.toString());
//			chart = false;
//		}
		if (!chart)
			super.onBackPressed();
	}

	@Override
	public void onWindowFocusChanged(boolean hasFocus)
	{
		super.onWindowFocusChanged(hasFocus);
//		if (!Cocos2dxHelper.isOnCreateCanGo()){
//			return;
//		}
		if (hasFocus)
		{
			this.hideSystemUI();
		}
	}

	private void hideSystemUI() {
		if(glSurfaceView == null) {
			return;
		}
		// Set the IMMERSIVE flag.
		// Set the content to appear under the system bars so that the content
		// doesn't resize when the system bars hide and show.
		glSurfaceView.setSystemUiVisibility(
				Cocos2dxGLSurfaceView.SYSTEM_UI_FLAG_LAYOUT_STABLE
						| Cocos2dxGLSurfaceView.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
						| Cocos2dxGLSurfaceView.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
						| Cocos2dxGLSurfaceView.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
						| Cocos2dxGLSurfaceView.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
						| Cocos2dxGLSurfaceView.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
	}

	@Override
	public void onTrimMemory(int level){
		if (level >= ComponentCallbacks2.TRIM_MEMORY_COMPLETE) {
			Cocos2dxHelper.callLuaGlobalFunctionWithStringOnGL("onLowMemory", String.valueOf(level));
		}
	}

	public void setLaunchPath(int flag){
		Log.d("DeepLink", "set mLaunchFlag:" + flag);
		mLaunchFlag = flag;
	}

	//1:default 2:url  3:local noification 4:remote noification 5:urlWithApp
	public int getLaunchPath(){
		Log.d("DeepLink", "mLaunchFlag:" + mLaunchFlag);
		return mLaunchFlag;
	}
}
