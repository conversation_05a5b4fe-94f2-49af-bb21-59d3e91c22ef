package org.cocos2dx.fcm;
import android.content.Intent;
import android.graphics.Bitmap;

import android.text.TextUtils;
import android.util.Log;

import com.adjust.sdk.Adjust;
import com.facebook.appevents.AppEventsLogger;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.helpshift.Helpshift;

import org.cocos2dx.bole.notification.BoleNotification;
import org.cocos2dx.lua.AppActivity;
import org.cocos2dx.lua.BoleApplication;
import org.cocos2dx.plugin.FacebookWrapper;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Map;


public class BoleFirebaseMessagingService extends FirebaseMessagingService {

    public static final String TAG = "FCMService";

    //http://jackpotworldcasino.com:8081/notification/v1.0/delivery/推送ID/设备类型
    private static final String serverNotifyUrl = "http://jackpotworldcasino.com:8081/notification/v1.0/delivery";
//    https://sea-web.jackpotworld.org/api/wide/hs-data/delivery/{ntf_id}?type=1
    private static final String serverHelpShiftUrl = "https://sea-web.jackpotworld.org/api/wide/hs-data";

    Bitmap bitmap;

    /**
     * Called when message is received.
     *
     * @param remoteMessage Object representing the message received from Firebase Cloud Messaging.
     */
    // [START receive_message]
    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        // [START_EXCLUDE]
        // There are two types of messages data messages and notification messages. Data messages are handled
        // here in onMessageReceived whether the app is in the foreground or background. Data messages are the type
        // traditionally used with GCM. Notification messages are only received here in onMessageReceived when the app
        // is in the foreground. When the app is in the background an automatically generated notification is displayed.
        // When the user taps on the notification they are returned to the app. Messages containing both notification
        // and data payloads are treated as notification messages. The Firebase console always sends notification
        // messages. For more see: https://firebase.google.com/docs/cloud-messaging/concept-options
        // [END_EXCLUDE]

        Log.d(TAG, "onMessageReceived log 1");
        Map<String, String> data = remoteMessage.getData();
        String origin = data.get("origin");
        if(origin != null && origin.equals("helpshift"))
        {
            Log.d(TAG, "onMessageReceived log 2");
            for(Map.Entry<String, String> entry : data.entrySet()) {
                Log.d(TAG, "data key = " + entry.getKey() + ", value = " + entry.getValue());
            }
            Helpshift.handlePush(data);
            return;
        }
        // 当游戏在前台时，不发推送。
        if(BoleApplication.isActivityVisible()) {
            Log.d(TAG, "onMessageReceived log 3");
            return;
        }
        Log.d(TAG, "onMessageReceived log 4");
        for(Map.Entry<String, String> entry : data.entrySet()) {
            Log.d(TAG, "data key = " + entry.getKey() + ", value = " + entry.getValue());
        }

        String id = data.get("id");
        String payType = data.get("pay_type");
        notifyReceivedToServer(serverNotifyUrl, id, "android", payType);
        String title = data.get("title");
        String content = data.get("body");
        String pic1 = data.get("img1");
        String pic2 = data.get("img2");
        String coins = data.get("coins");
        String dollar = data.get("dollar");
        Intent intent = new Intent(this, AppActivity.class);
        intent.putExtra("jwLaunch", 4);
        if(!TextUtils.isEmpty(id)) {
            intent.putExtra("id", id);
        }
        if(!TextUtils.isEmpty(coins)) {
            intent.putExtra("coins", Integer.valueOf(coins));
        }
        if(!TextUtils.isEmpty(dollar)) {
            intent.putExtra("dollar", Double.valueOf(dollar));
        }
        intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

        if(!TextUtils.isEmpty(id) && !id.isEmpty() && id.startsWith("helpShift_new")){
            //收到客服推送发消息进入后端
            notifyReceivedToServer(serverHelpShiftUrl, "delivery", id, "type=1");
        }

        if(TextUtils.isEmpty(pic1) && TextUtils.isEmpty(pic2)) {
            BoleNotification.showCustomNotificationWithOnlyText(this, title, content, intent);
        } else if(!TextUtils.isEmpty(pic1) && TextUtils.isEmpty(pic2)) {
            BoleNotification.showCustomNotificationWithOnePicture(this, title, content, pic1, intent);
        } else if(TextUtils.isEmpty(pic1) && !TextUtils.isEmpty(pic2)) {
            BoleNotification.showCustomNotificationWithOnePicture(this, title, content, pic2, intent);
        } else {
            BoleNotification.showCustomNotificationWithTwoPictures(this, title, content, pic1, pic2, intent);
        }
    }
    // [END receive_message]

    // 令牌刷新：重装app，清除缓存，项目配置变更
    @Override
    public void onNewToken(String token) {
        try {
            Log.d(TAG, "Refreshed token: " + token);

            FCMRegistrar.getInstance().setToken(token);

            FirebaseMessaging.getInstance().subscribeToTopic("bole");
            FacebookWrapper.setPushNotificationsRegistrationId(token);

            // If you want to send messages to this application instance or
            // manage this apps subscriptions on the server side, send the
            // Instance ID token to your app server.

            //send token to server by script
            Adjust.setPushToken(token, this);

            Helpshift.registerPushToken(token);

        } catch (Exception e) {
            Log.d(TAG, "Failed to complete token refresh", e);
        }
    }

    private void notifyReceivedToServer(String serverUrl, String id, String platform, String payType) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                String urlstr = String.format("%s/%s/%s/%s", serverUrl, id, platform, payType);
                if(payType != null && !payType.isEmpty() && payType.equals("type=1")){
                    urlstr = String.format("%s/%s/%s?%s", serverUrl, id, platform, payType);
                }
                Log.d(TAG, "urlstr:"+urlstr);
                try {
                    URL url = new URL(urlstr);
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("GET");
                    connection.connect();
                    int responseCode = connection.getResponseCode();
                    if(responseCode == HttpURLConnection.HTTP_OK) {
                        InputStream inputStream = connection.getInputStream();
                        inputStream.read();
                    }
                } catch (MalformedURLException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    if(connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();
    }
}
