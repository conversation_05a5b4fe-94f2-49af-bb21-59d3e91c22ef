package org.cocos2dx.fcm;

import android.app.Activity;
import android.os.Bundle;
import androidx.annotation.NonNull;

import android.text.TextUtils;
import android.util.Log;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;

import org.cocos2dx.lib.Cocos2dxHelper;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.EnumMap;
import java.util.Iterator;
import java.util.Map;

public class FireBaseManager {

    /**
     * Tag used on log messages.
     */
    static final String TAG = "FireBaseManager";

	public static String register() {
        return FCMRegistrar.getInstance().getToken();
	}
	
	public static void removeRegistrationId() {
        FCMRegistrar.getInstance().removeRegistrationId();
    }

    public static void subscribeToTopic(String topic) {
        FirebaseMessaging.getInstance().subscribeToTopic(topic);
    }

    public static void unsubscribeFromTopic(String topic) {
        FirebaseMessaging.getInstance().unsubscribeFromTopic(topic);
    }

    public static void logEvent(String eventName, String otherInfo) {
        Log.i("FirebaseLogEvent", "eventName=" + eventName + " otherInfo=" + otherInfo);
        Activity a = Cocos2dxHelper.getActivity();
        if (a == null) {
            return;
        }

        FirebaseAnalytics logger = FirebaseAnalytics.getInstance(a.getApplicationContext());
        Bundle params = new Bundle();
        if(!TextUtils.isEmpty(otherInfo)) {
            try {
                JSONObject info = new JSONObject(otherInfo);
                Iterator<String> keysItr = info.keys();
                while(keysItr.hasNext()) {
                    String key = keysItr.next();
                    Object value = info.get(key);
                    if(value instanceof String) {
                        params.putString(key, (String)value);
                    }else if(value instanceof Integer) {
                        params.putLong(key, (Integer)value);
                    }else if(value instanceof Double) {
                        params.putDouble(key, (Double)value);
                    }
                }
                if(info.has("user_id")) {
                    logger.setUserId(info.getString("user_id"));
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        logger.logEvent(eventName, params);
    }

    public static void fetchRemoteConfig(final String jsonData) {
        Activity a = Cocos2dxHelper.getActivity();
        if (a == null) {
            return;
        }

	//        long cacheExpiration = 12 * 3600;
        FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
	    //      .setMinimumFetchIntervalInSeconds(cacheExpiration)
                .build();
        FirebaseRemoteConfig.getInstance().setConfigSettingsAsync(configSettings);

        FirebaseRemoteConfig.getInstance().fetchAndActivate()
                .addOnCompleteListener(a, new OnCompleteListener<Boolean>() {
                    @Override
                    public void onComplete(@NonNull Task<Boolean> task) {
                        if (task.isSuccessful()) {
                            boolean updated = task.getResult();
                            Log.d(TAG, "Config params updated: " + updated);
//                            Toast.makeText(MainActivity.this, "Fetch and activate succeeded",
//                                    Toast.LENGTH_SHORT).show();

                        } else {
                            Log.d(TAG, "Fetch failed.");
//                            Toast.makeText(MainActivity.this, "Fetch failed",
//                                    Toast.LENGTH_SHORT).show();
                        }
                        if (FirebaseRemoteConfig.getInstance().getString("will_pay").equals("true")) {
                            Log.d(TAG, "log will_pay.");
                            Activity a = Cocos2dxHelper.getActivity();
                            FirebaseAnalytics logger = FirebaseAnalytics.getInstance(a.getApplicationContext());
                            Bundle bundle = new Bundle();
                            if(!TextUtils.isEmpty(jsonData)) {
                                try {
                                    JSONObject json = new JSONObject(jsonData);
                                    Iterator<String> keys = json.keys();
                                    while(keys.hasNext()) {
                                        String key = keys.next();
                                        Object value = json.get(key);
                                        if(value instanceof String) {
                                            bundle.putString(key, (String)value);
                                        }else if(value instanceof Integer) {
                                            bundle.putLong(key, (Integer)value);
                                        }else if(value instanceof Double) {
                                            bundle.putDouble(key, (Double)value);
                                        }
                                    }
                                    if(json.has("user_id")) {
                                        logger.setUserId(json.getString("user_id"));
                                    }
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            }

                            logger.logEvent("will_pay", bundle);
                        }
                    }
                });
    }
//    public static String getRCValueJSONForKey(String key) {
//        // not supported
//    }
    public static String getRCValueStringForKey(String key) {
        return FirebaseRemoteConfig.getInstance().getString(key);
    }
    public static double getRCValueNumberForKey(String key) {
        return FirebaseRemoteConfig.getInstance().getDouble(key);
    }

    public static void setConsent(Map<String, Boolean> cMap) {
        Activity a = Cocos2dxHelper.getActivity();
        FirebaseAnalytics mFirebaseAnalytics = FirebaseAnalytics.getInstance(a.getApplicationContext());
        Map<FirebaseAnalytics.ConsentType, FirebaseAnalytics.ConsentStatus> consentMap = new EnumMap<>(FirebaseAnalytics.ConsentType.class);
        if(Boolean.TRUE.equals(cMap.get("ANALYTICS_STORAGE"))) {
            consentMap.put(FirebaseAnalytics.ConsentType.ANALYTICS_STORAGE, FirebaseAnalytics.ConsentStatus.GRANTED);
        }
        if(Boolean.TRUE.equals(cMap.get("AD_STORAGE"))) {
            consentMap.put(FirebaseAnalytics.ConsentType.AD_STORAGE, FirebaseAnalytics.ConsentStatus.GRANTED);
        }
        if(Boolean.TRUE.equals(cMap.get("AD_USER_DATA"))) {
            consentMap.put(FirebaseAnalytics.ConsentType.AD_USER_DATA, FirebaseAnalytics.ConsentStatus.GRANTED);
        }
        if(Boolean.TRUE.equals(cMap.get("AD_PERSONALIZATION"))) {
            consentMap.put(FirebaseAnalytics.ConsentType.AD_PERSONALIZATION, FirebaseAnalytics.ConsentStatus.GRANTED);
        }
//        Log.d(TAG, "mFirebaseAnalytics.setConsent(consentMap), map=" + consentMap.toString());
        mFirebaseAnalytics.setConsent(consentMap);
    }
}
