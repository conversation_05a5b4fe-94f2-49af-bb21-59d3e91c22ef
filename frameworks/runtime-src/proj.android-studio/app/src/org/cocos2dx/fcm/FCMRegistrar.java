package org.cocos2dx.fcm;

//import com.google.android.gms.common.ConnectionResult;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.util.Log;


import org.cocos2dx.lua.AppActivity;

public class FCMRegistrar {

    public static final String EXTRA_MESSAGE = "message";
    public static final String PROPERTY_REG_ID = "registration_id";
    public static final String PROPERTY_APP_VERSION = "appVersion";
    public static final int PLAY_SERVICES_RESOLUTION_REQUEST = 9000;

    Activity activity;
    Context context;
    private String _regid = "";
    static String TAG = "FCMRegistrar";

    public static FCMRegistrar _instance = null;
    public static FCMRegistrar getInstance(){
        if(_instance == null){
             _instance = new FCMRegistrar();
        }
        return _instance;
    }

    public FCMRegistrar(){

    }

    public void init(Activity act){
        activity = act;
        context = act.getApplicationContext();
        _regid = getRegistrationId(activity);
    }

    public String getToken(){
        return _regid;
    }

    public void setToken(String regId){
        storeRegistrationId(regId);
        _regid = regId;
    }

    /**
     * Stores the registration ID and the app versionCode in the application's
     * {@code SharedPreferences}.
     *setToken
     * @param regId registration ID
     */
    private void storeRegistrationId(String regId) {
        Log.d(TAG, "storeRegistrationId :" + regId);
//        activity = Cocos2dxHelper.getActivity();
//        context = activity.getApplicationContext();
        final SharedPreferences prefs = getGcmPreferences(context);
        if (prefs == null){
            Log.i(TAG,"SharedPreferences is null");
            return ;
        }
//        int appVersion = getAppVersion(context);
//        Log.i(TAG, "Saving regId on app version " + appVersion);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(PROPERTY_REG_ID, regId);
//        editor.putInt(PROPERTY_APP_VERSION, appVersion);
        editor.apply();
    }
    
    /**
     * remove the registration ID in the application's
     * {@code SharedPreferences}.
     *
     */
    public void removeRegistrationId() {
//        activity = Cocos2dxHelper.getActivity();
//        context = activity.getApplicationContext();

		if (context !=null){
            final SharedPreferences prefs = getGcmPreferences(context);
            if (prefs == null){
                Log.i(TAG,"SharedPreferences is null");
                return ;
            }
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(PROPERTY_REG_ID, "");
            editor.apply();
        }
    }
    
    /**
     * Gets the current registration ID for application on GCM service, if there is one.
     * <p>
     * If result is empty, the app needs to register.
     *
     * @return registration ID, or empty string if there is no existing
     *         registration ID.
     */
    private String getRegistrationId(Context context) {
        final SharedPreferences prefs = getGcmPreferences(context);
        if (prefs == null){
            Log.i(TAG,"SharedPreferences is null");
            return "";
        }
        String registrationId = prefs.getString(PROPERTY_REG_ID, "");
        if (registrationId.isEmpty()) {
            Log.i(TAG, "Registration not found.");
            return "";
        }
        Log.i(TAG,"Registration " + registrationId);
        return registrationId;
    }

    /**
     * @return Application's version code from the {@code PackageManager}.
     */
    private static int getAppVersion(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (NameNotFoundException e) {
            // should never happen
            throw new RuntimeException("Could not get package name: " + e);
        }
    }
    
    /**
     * @return Application's {@code SharedPreferences}.
     */
    private SharedPreferences getGcmPreferences(Context context) {
        // This sample app persists the registration ID in shared preferences, but
        // how you store the regID in your app is up to you.
        if(activity != null){
//            Log.i(TAG,"AppName1 is" + activity.getClass().getAnnotations().toString());
//            Log.i(TAG,"AppName2 is" + AppActivity.class.getSimpleName());
            return activity.getSharedPreferences(AppActivity.class.getSimpleName(),
                    Context.MODE_PRIVATE);
        }
        Log.i(TAG,"getGcmPreferences() but  is null");
        return null;
    }
}
