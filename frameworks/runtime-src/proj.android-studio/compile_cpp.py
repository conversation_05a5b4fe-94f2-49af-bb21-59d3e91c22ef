#!/usr/bin/python

from GlobalCfg import *
from optparse import OptionParser
import sys
import os
import glob

compile_cmd_template = '''%s -j8 -C %s/frameworks/runtime-src/proj.android-studio/app NDK_DEBUG=%d \
NDK_MODULE_PATH=%s/frameworks/cocos2d-x:\
%s/frameworks/cocos2d-x/cocos:\
%s/frameworks/cocos2d-x/external:\
%s/frameworks/cocos2d-x/cocos/scripting'''

def verify_mode(mode):
    return mode == 'debug' or mode == 'release' or mode == 'all'

def fix_archive_symbol_tables():
    """Fix archive symbol table issues by running ranlib on static libraries"""

    # Find all .a files in the obj directory
    archive_patterns = [
        'app/obj/local/*/libcocos2d.a',
        'app/obj/local/*/libluacocos2d.a',
        'app/obj/local/*/libcocos2dxinternal.a'
    ]

    # Get the ranlib tool from NDK
    ranlib_tool = None
    ndk_toolchains = [
        '%s/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ranlib' % ndk_path,
        '%s/toolchains/arm-linux-androideabi-4.9/prebuilt/darwin-x86_64/bin/arm-linux-androideabi-ranlib' % ndk_path,
        '%s/toolchains/aarch64-linux-android-4.9/prebuilt/darwin-x86_64/bin/aarch64-linux-android-ranlib' % ndk_path,
        '%s/toolchains/x86-4.9/prebuilt/darwin-x86_64/bin/i686-linux-android-ranlib' % ndk_path
    ]

    for tool in ndk_toolchains:
        if os.path.exists(tool):
            ranlib_tool = tool
            break

    if not ranlib_tool:
        print("Warning: Could not find ranlib tool in NDK")
        return

    # Apply ranlib to all found archive files
    for pattern in archive_patterns:
        archives = glob.glob(pattern)
        for archive in archives:
            if os.path.exists(archive):
                print("Running ranlib on %s" % archive)
                os.system('%s "%s"' % (ranlib_tool, archive))

def compile(mode):
	compile_mode = 1 if mode == 'debug' else 0

	# Clean build first to avoid archive issues
	print("Cleaning previous build...")
	clean_cmd = '%s -C %s/frameworks/runtime-src/proj.android-studio/app clean' % (ndk_path, proj_path)
	os.system(clean_cmd)

	cmd = compile_cmd_template % (ndk_path, proj_path, compile_mode, proj_path, proj_path, proj_path, proj_path)
	print(cmd)

	# Run the build command
	result = os.system(cmd)

	# If build failed, try to fix archive symbol table issues
	if result != 0:
		print("Build failed, attempting to fix archive symbol table issues...")
		fix_archive_symbol_tables()
		print("Retrying build...")
		result = os.system(cmd)
		if result != 0:
			print("Build failed even after attempting fixes")
			return

	if os.path.exists('app/src/main/jniLibs-%s/' % mode):
		os.system('rm -fr app/src/main/jniLibs-%s/' % mode)

	os.mkdir('app/src/main/jniLibs-%s' % mode)

	os.system('mv app/libs/x86 app/src/main/jniLibs-%s/' % mode)
	os.system('mv app/libs/armeabi-v7a app/src/main/jniLibs-%s/' % mode)
	os.system('mv app/libs/arm64-v8a app/src/main/jniLibs-%s/' % mode)

def main():
    parser = OptionParser()
    parser.add_option('-m', '--mode', default='debug', dest='mode', help='mode, debug or release')

    os.chdir(os.path.join(proj_path, 'frameworks/runtime-src/proj.android-studio'))

    (opts, _) = parser.parse_args()
    if not verify_mode(opts.mode):
        print('invalid mode, debug or release or all please')
        return 1

    if opts.mode == 'all':
        compile('debug')
        compile('release')
    else:
        compile(opts.mode)
main()
