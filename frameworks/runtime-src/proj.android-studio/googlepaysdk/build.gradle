apply plugin: 'com.android.library'

android {
    namespace "com.frontier.googlepaysdk"
    compileSdk 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation project(':sdkbase')
    implementation "com.android.billingclient:billing:7.1.1"
}