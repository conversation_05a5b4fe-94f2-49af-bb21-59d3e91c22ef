package com.frontier.googlepaysdk;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingFlowParams;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ConsumeParams;
import com.android.billingclient.api.ConsumeResponseListener;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.PurchasesResponseListener;
import com.android.billingclient.api.PurchasesUpdatedListener;
import com.android.billingclient.api.SkuDetails;
import com.android.billingclient.api.SkuDetailsParams;
import com.android.billingclient.api.SkuDetailsResponseListener;
import com.frontier.sdkbase.AbstractPayImpl;
import com.frontier.sdkbase.ErrorCode;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKType;

import com.android.billingclient.api.*;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.QueryProductDetailsParams;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class GooglePayHelper extends AbstractPayImpl {
    private static final String TAG = "GooglePayHelper";
    private Map<String, SkuDetails> skuMap;

    // 将 skuMap 改为 productDetailsMap 存储 ProductDetails
    private Map<String, ProductDetails> productDetailsMap;
    private Boolean isNewVersion = false;

    private BillingClient billingClient;
    private Boolean isBillingClientConnected = false;
    private Map<String, Purchase> purchaseDatas;
    private String skuList;
    private Integer billingSupportedStatus = -1;
    private String m_countryCode;
    private Boolean _hasSkuItem = false;
    private String _sendItemId;
    private String _getProductId;
    private String _sendPayload;
    public GooglePayHelper(Activity activity, final EventListener listener) {
        super(activity, listener);
        // Log.d("scene", "GooglePayHelper 12");
        productDetailsMap = new HashMap<>();
        skuMap = new HashMap<>();
        purchaseDatas = new HashMap<>();
        _sendItemId = "";
        _getProductId = "";
        _sendPayload = "";
        // Log.d("scene", "GooglePayHelper 13");
        billingClient = BillingClient.newBuilder(activity).setListener(new PurchasesUpdatedListener() {
            @Override
            public void onPurchasesUpdated(BillingResult billingResult, List<Purchase> purchases) {
                // 刷新支付数据，onPurchase后也会刷新数据
                Log.d(TAG, "onPurchasesUpdated result = " + billingResult.getResponseCode() + ", msg = " + billingResult.getDebugMessage());
                if(billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK && purchases != null) {
                    for(Purchase purchase : purchases) {
                        if(purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                            callBackWithPurchaseData(purchase, false, false);
                        }else if(purchase.getPurchaseState() == Purchase.PurchaseState.PENDING){
                            listener.onPurchase(sdkType(), ErrorCode.ALREADY_IN_PROCESS, "", "", "", "", null, 0);
                        }else{
                            JSONObject json = new JSONObject();
                            try {
                                json.put("purchaseState", purchase.getPurchaseState());
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                            listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", json, 0);
                        }
                    }
                } else if(billingResult.getResponseCode() == BillingClient.BillingResponseCode.USER_CANCELED) {
                    listener.onPurchase(sdkType(), ErrorCode.USER_CANCELED, "", "", "", "", null, 0);
                } else {
                    JSONObject json = new JSONObject();
                    try {
                        json.put("responseCode", billingResult.getResponseCode());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", json, 0);
                }
            }
        }).enablePendingPurchases(PendingPurchasesParams.newBuilder().enableOneTimeProducts().build()).build();

        // Log.d("scene", "GooglePayHelper 14");
        startBillingConnection(true);
        // Log.d("scene", "GooglePayHelper 15");
    }

    private void callBackWithPurchaseData(Purchase purchase, boolean replenishment, boolean alreadyHasPay) {
        if(purchaseDatas.containsKey(purchase.getOrderId())) {
            purchaseDatas.remove(purchase.getOrderId());
        }
        purchaseDatas.put(purchase.getOrderId(), purchase);
        JSONObject json = new JSONObject();
        try {
            json.put("package_name", purchase.getPackageName());
            json.put("start_time", purchase.getPurchaseTime());
            Log.d(TAG, "originJson = " + purchase.getOriginalJson());
            if(alreadyHasPay){
                json.put("alreadyHasPay", "1");
            }
            if(!_hasSkuItem){
                json.put("noHasSkuItem", "1");
            }
            if(!TextUtils.isEmpty(_sendItemId)){
                json.put("sendItemId", _sendItemId);
            }
            if(!TextUtils.isEmpty(_getProductId)){
                json.put("getProductId", _getProductId);
            }
            if(!TextUtils.isEmpty(_sendPayload)){
                json.put("sendPayload", _sendPayload);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String leftEncoded = purchase.getAccountIdentifiers().getObfuscatedAccountId();
        String rightEncoded = purchase.getAccountIdentifiers().getObfuscatedProfileId();
        if(!TextUtils.isEmpty(leftEncoded)) {
            leftEncoded = leftEncoded.substring(1);
        }

        if(!TextUtils.isEmpty(rightEncoded)) {
            rightEncoded = rightEncoded.substring(1);
        }

        String encoded = leftEncoded + rightEncoded;
        String payload = "";
        if(!TextUtils.isEmpty(encoded)) {
            byte[] data = Base64.decode(encoded, Base64.DEFAULT);
            try {
                payload = new String(data, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }

        String sku = purchase.getProducts().get(0);
        if(replenishment) {
            listener.onUndeliveredOrder(sdkType(),
                    ErrorCode.NO_ERROR,
                    sku,
                    purchase.getOrderId(),
                    payload,
                    purchase.getPurchaseToken(),
                    json,
                    0);
        } else {
            listener.onPurchase(sdkType(),
                    ErrorCode.NO_ERROR,
                    sku,
                    purchase.getOrderId(),
                    payload,
                    purchase.getPurchaseToken(),
                    json,
                    0);
        }
    }

    private void startBillingConnection(final boolean isInited) {
        if(!isBillingClientConnected) {
            billingClient.startConnection(new BillingClientStateListener() {
                @Override
                public void onBillingSetupFinished(final BillingResult billingResult) {
                    Log.d(TAG, "connection result = " + billingResult.getResponseCode() + ", msg = " + billingResult.getDebugMessage());
                    isBillingClientConnected = billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK;
                    if(!isBillingClientConnected && !isInited) {
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (!activity.isFinishing()) {
                                    AlertDialog.Builder builder = new AlertDialog.Builder(activity, android.R.style.Theme_Material_Dialog_Alert);
                                    builder.setCancelable(true)
                                            .setTitle(R.string.sdk_not_inited_title)
                                            .setMessage(billingResult.getDebugMessage())
                                            .setPositiveButton(R.string.confirm_txt, new DialogInterface.OnClickListener() {
                                                @Override
                                                public void onClick(DialogInterface dialog, int which) {

                                                }
                                            });
                                    builder.create().show();
                                }
                            }
                        });
                    }
                    if(!TextUtils.isEmpty(skuList)) {
                        String tmp = skuList;
                        skuList = null;
                        querySkuDetails(tmp);
                    }

                    if(isBillingClientConnected){
                        getBillingConfigAsync();
                    }
                }

                @Override
                public void onBillingServiceDisconnected() {
                    isBillingClientConnected = false;
                }
            });
        }
    }

    @Override
    public int sdkType() {
        return SDKType.GOOGLE_PAY;
    }

    @Override
    public boolean isSupportSDKType(int sdkType) {
        return sdkType == sdkType();
    }

    @Override
    public void purchase(final String itemId, final String payload, final JSONObject jsonData) {
        if(isNewVersion()){
            purchase2(itemId, payload, jsonData);
            return;
        }
        if(!isBillingClientConnected) {
            startBillingConnection(false);
            listener.onPurchase(sdkType(), ErrorCode.SDK_NOT_INITED, "", "", "", "", null, 0);
            return;
        }
        if(skuMap.containsKey(itemId)) {
            _hasSkuItem = true;
            queryAndLaunchPurchaseFlow(itemId, payload, jsonData);
        } else {
            _hasSkuItem = false;
            List<String> skuList = new ArrayList<>();
            skuList.add(itemId);
            SkuDetailsParams.Builder builder = SkuDetailsParams.newBuilder();
            builder.setSkusList(skuList).setType(BillingClient.SkuType.INAPP);
            billingClient.querySkuDetailsAsync(builder.build(), new SkuDetailsResponseListener() {
                @Override
                public void onSkuDetailsResponse(BillingResult billingResult, List<SkuDetails> skuDetails) {
                    if(skuDetails != null) {
                        for(SkuDetails skuDetail : skuDetails) {
                            skuMap.put(skuDetail.getSku(), skuDetail);
                        }
                    }
                    if(skuMap.containsKey(itemId)) {
                        queryAndLaunchPurchaseFlow(itemId, payload, jsonData);
                    } else {
                        listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                    }
                }
            });
        }
    }

    @Override
    public void querySkuDetails(String skuList) {
        if(isNewVersion()){
            querySkuDetails2(skuList);
            return;
        }
        if(isBillingClientConnected) {
            String[] skuListArray = skuList.split(",");
            final List<String> list = java.util.Arrays.asList(skuListArray);
            SkuDetailsParams.Builder builder = SkuDetailsParams.newBuilder();
            builder.setSkusList(list).setType(BillingClient.SkuType.INAPP);
            billingClient.querySkuDetailsAsync(builder.build(), new SkuDetailsResponseListener() {
                @Override
                public void onSkuDetailsResponse(BillingResult billingResult, List<SkuDetails> skuDetails) {
                    if(skuDetails != null) {
                        JSONArray skuArray = new JSONArray();
                        for(SkuDetails skuDetail : skuDetails) {
                            String productId = skuDetail.getSku();
                            String price = skuDetail.getPrice();
                            String description = skuDetail.getDescription();
                            String title = skuDetail.getTitle();
                            long priceAmountMicros = skuDetail.getIntroductoryPriceAmountMicros();
                            skuMap.put(productId, skuDetail);
                            JSONObject json = new JSONObject();
                            try {
                                json.put("product_id", productId);
                                json.put("price", price);
                                json.put("description", description);
                                json.put("title", title);
                                json.put("price_amount_micros", priceAmountMicros);
                                skuArray.put(json);
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }
                        listener.onQuerySkuDetails(sdkType(), ErrorCode.NO_ERROR, skuArray, 0);
                    }
                }
            });
        } else {
            this.skuList = skuList;
        }
    }

    private void queryAndLaunchPurchaseFlow(final String itemId, final String payload, final JSONObject jsonData) {
        if(isNewVersion()) {
            queryAndLaunchPurchaseFlow2(itemId, payload, jsonData);
            return;
        }
        billingClient.queryPurchasesAsync(BillingClient.SkuType.INAPP, new PurchasesResponseListener() {
            @Override
            public void onQueryPurchasesResponse(BillingResult billingResult, List<Purchase> purchases) {
                if((billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) && (purchases != null) && (purchases.size() > 0)) {
                    for(final Purchase purchase : purchases) {
                        ArrayList<String> skus = purchase.getSkus();
                        for(String sku : skus) {
                            if(sku.equals(itemId)) {
                                activity.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        AlertDialog.Builder builder = new AlertDialog.Builder(activity, android.R.style.Theme_Material_Dialog_Alert);
                                        builder.setMessage(R.string.restore_order_tip)
                                                .setCancelable(false)
                                                .setPositiveButton(R.string.confirm_txt, new DialogInterface.OnClickListener() {
                                                    @Override
                                                    public void onClick(DialogInterface dialog, int which) {
                                                        // 这里的补单是玩家再次点击相同的商品发起的，所以replenishment置为false
                                                        // 只有游戏启动时的自动检测补单replenishment置为true
                                                        if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                                                            callBackWithPurchaseData(purchase, false, true);
                                                        }else if(purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
                                                            listener.onPurchase(sdkType(), ErrorCode.ALREADY_IN_PROCESS, "", "", "", "", null, 0);
                                                        }else{
                                                            JSONObject json = new JSONObject();
                                                            try {
                                                                json.put("purchaseState", purchase.getPurchaseState());
                                                            } catch (JSONException e) {
                                                                e.printStackTrace();
                                                            }
                                                            listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", json, 0);
                                                        }
                                                    }
                                                });
                                        builder.create().show();
                                    }
                                });

                                return;
                            }
                        }
                    }

                    launchPurchaseFlow(itemId, payload, jsonData);
                } else {
                    launchPurchaseFlow(itemId, payload, jsonData);
                }
            }
        });
    }

    private char getRandomPrefix() {
        String alphbet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        int index = random.nextInt(alphbet.length());
        return alphbet.charAt(index);
    }

    // 开始发起支付 启动购买流程
    private void launchPurchaseFlow(String itemId, String payload, JSONObject jsonData) {
        if(isNewVersion()){
            launchPurchaseFlow2(itemId, payload, jsonData);
            return;
        }
        SkuDetails skuDetails = skuMap.get(itemId);
        String leftPayload = "";
        String rightPayload = "";

        // 以下对payload的处理是因为Gooble IAP v3.0不允许AccountId和ProfileId包含玩家信息，所以有必要做一些混淆，否则发起支付会失败
        if(!TextUtils.isEmpty(payload)) {
            try {
                byte[] data = payload.getBytes("UTF-8");
                String encoded = Base64.encodeToString(data, Base64.DEFAULT);
                leftPayload = getRandomPrefix() + encoded.substring(0, encoded.length()/2);
                rightPayload = getRandomPrefix() + encoded.substring(encoded.length()/2);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }

        BillingFlowParams billingFlowParams = BillingFlowParams.newBuilder()
                .setSkuDetails(skuDetails)
                .setObfuscatedAccountId(leftPayload)
                .setObfuscatedProfileId(rightPayload)
                .build();
        int responseCode = billingClient.launchBillingFlow(activity, billingFlowParams).getResponseCode();
        Log.d(TAG, "launchPurchaseFlow result = " + responseCode);
    }

    // 支付成功之后，lua发往服务器，服务器返回后
    @Override
    public void consumePurchase(String orderId) {
        if(purchaseDatas.containsKey(orderId)) {
            Purchase purchase = purchaseDatas.get(orderId);
            ConsumeParams consumeParams = ConsumeParams.newBuilder()
                    .setPurchaseToken(purchase.getPurchaseToken())
                    .build();
            ConsumeResponseListener listener = new ConsumeResponseListener() {
                @Override
                public void onConsumeResponse(BillingResult billingResult, String s) {
                    if (billingResult.getResponseCode() != BillingClient.BillingResponseCode.OK) {
                        Log.e(TAG, "Failed to consume purchase: " + billingResult.getDebugMessage());
                    } else {
                        Log.d(TAG, "consumePurchase result = " + billingResult.getResponseCode());
                    }
                }
            };
            billingClient.consumeAsync(consumeParams, listener);
            purchaseDatas.remove(orderId);
        }
    }

    // 检查未支付的订单
    @Override
    public void checkUnDeliveredOrders() {
        if(isNewVersion()){
            checkUnDeliveredOrders2();
            return;
        }
        billingClient.queryPurchasesAsync(BillingClient.SkuType.INAPP, new PurchasesResponseListener() {
            @Override
            public void onQueryPurchasesResponse(BillingResult billingResult, List<Purchase> purchases) {
                if((billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) && (purchases != null)) {
                    for(Purchase purchase : purchases) {
                        if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                            callBackWithPurchaseData(purchase, true, false);
                        }
                    }
                }
                listener.onUndeliveredOrder(SDKType.GOOGLE_PAY,
                        ErrorCode.NO_MORE_UNDELIVERED_ORDER,
                        "",
                        "",
                        "",
                        "",
                        null,
                        0);
            }
        });
    }

    public void purchase2(final String itemId, final String payload, final JSONObject jsonData) {
        Log.d(TAG, "new purchase2:");
        if (!isBillingClientConnected) {
            startBillingConnection(false);
            listener.onPurchase(sdkType(), ErrorCode.SDK_NOT_INITED, "", "", "", "", null, 0);
            return;
        }

        if (productDetailsMap.containsKey(itemId)) {
            _hasSkuItem = true;
            queryAndLaunchPurchaseFlow2(itemId, payload, jsonData);
        } else {
            _hasSkuItem = false;
            List<QueryProductDetailsParams.Product> productList = new ArrayList<>();
            productList.add(QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(itemId)
                    .setProductType(BillingClient.ProductType.INAPP)
                    .build());
            QueryProductDetailsParams queryProductDetailsParams =
                    QueryProductDetailsParams.newBuilder()
                            .setProductList(productList)
                            .build();

            billingClient.queryProductDetailsAsync(queryProductDetailsParams,
                    new ProductDetailsResponseListener() {
                        @Override
                        public void onProductDetailsResponse(@NonNull BillingResult billingResult,
                                                             @NonNull List<ProductDetails> productDetailsList) {
                            if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                                for (ProductDetails productDetails : productDetailsList) {
                                    productDetailsMap.put(productDetails.getProductId(), productDetails);
                                }
                                if (productDetailsMap.containsKey(itemId)) {
                                    queryAndLaunchPurchaseFlow2(itemId, payload, jsonData);
                                } else {
                                    // 处理商品不存在的情况
                                    listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                                }
                            }
                        }
                    });
        }
    }

    public void querySkuDetails2(String skuList) {
        if (isBillingClientConnected) {
            String[] skuListArray = skuList.split(",");
            List<QueryProductDetailsParams.Product> products = new ArrayList<>();
            for (String sku : skuListArray) {
                products.add(QueryProductDetailsParams.Product.newBuilder()
                        .setProductId(sku)
                        .setProductType(BillingClient.ProductType.INAPP)
                        .build());
            }

            QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                    .setProductList(products)
                    .build();

            billingClient.queryProductDetailsAsync(params,
                    new ProductDetailsResponseListener() {
                        @Override
                        public void onProductDetailsResponse(@NonNull BillingResult billingResult,
                                                             @NonNull List<ProductDetails> productDetailsList) {
                            JSONArray skuArray = new JSONArray();
                            for (ProductDetails productDetails : productDetailsList) {
                                try {
                                    JSONObject json = new JSONObject();
                                    json.put("product_id", productDetails.getProductId());
                                    // 获取价格信息需要从 PricingPhase 获取
                                    ProductDetails.OneTimePurchaseOfferDetails pricingPhases = productDetails.getOneTimePurchaseOfferDetails();
                                    if (pricingPhases != null) {
                                        json.put("price", pricingPhases.getFormattedPrice());
                                        json.put("price_amount_micros", pricingPhases.getPriceAmountMicros());
                                    }
                                    json.put("description", productDetails.getDescription());
                                    json.put("title", productDetails.getTitle());
                                    skuArray.put(json);
                                    productDetailsMap.put(productDetails.getProductId(), productDetails);
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            }
                            listener.onQuerySkuDetails(sdkType(), ErrorCode.NO_ERROR, skuArray, 0);
                        }
                    });
        } else {
            this.skuList = skuList;
        }
    }

    private void queryAndLaunchPurchaseFlow2(final String itemId, final String payload, final JSONObject jsonData) {
        // 使用新的 QueryPurchasesParams 构建查询参数
        QueryPurchasesParams queryParams = QueryPurchasesParams.newBuilder()
                .setProductType(BillingClient.ProductType.INAPP)
                .build();

        billingClient.queryPurchasesAsync(
                queryParams,
                new PurchasesResponseListener() {
                    @Override
                    public void onQueryPurchasesResponse(@NonNull BillingResult billingResult, @NonNull List<Purchase> purchases) {
                        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                            if (!purchases.isEmpty()) {
                                for (final Purchase purchase : purchases) {
                                    // 注意：使用 getProducts() 替代已弃用的 getSkus()
                                    List<String> products = purchase.getProducts();
                                    for (String productId : products) {
                                        if (productId.equals(itemId)) {
                                            handleExistingPurchase(purchase);
                                            return; // 发现未完成订单后直接返回
                                        }
                                    }
                                }
                            }
                        }
                        // 没有未完成订单时继续购买流程
                        launchPurchaseFlow2(itemId, payload, jsonData);
                    }
                });
    }

    private void handleExistingPurchase(Purchase purchase) {
//        Log.d(TAG, "存在未支付完成的情况：");
        activity.runOnUiThread(() -> {
            AlertDialog.Builder builder = new AlertDialog.Builder(activity, android.R.style.Theme_Material_Dialog_Alert);
            builder.setMessage(R.string.restore_order_tip)
                    .setCancelable(false)
                    .setPositiveButton(R.string.confirm_txt, (dialog, which) -> {
                        // 处理补单逻辑
                        if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                            callBackWithPurchaseData(purchase, false, true);
                        } else if (purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
                            listener.onPurchase(sdkType(), ErrorCode.ALREADY_IN_PROCESS,
                                    "", "", "", "", null, 0);
                        } else {
                            JSONObject json = new JSONObject();
                            try {
                                json.put("purchaseState", purchase.getPurchaseState());
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                            listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR,
                                    "", "", "", "", json, 0);
                        }
                    });
            builder.create().show();
        });
    }

    // 启动购买流程
    private void launchPurchaseFlow2(String itemId, String payload, JSONObject jsonData) {
        ProductDetails productDetails = productDetailsMap.get(itemId);
        if (productDetails == null) return;

        _sendItemId = itemId;
        _getProductId = productDetails.getProductId();
        _sendPayload = payload;

        // 构建购买参数
        BillingFlowParams.ProductDetailsParams productDetailsParams =
                BillingFlowParams.ProductDetailsParams.newBuilder()
                        .setProductDetails(productDetails)
                        .build();

        List<BillingFlowParams.ProductDetailsParams> productDetailsParamsList = new ArrayList<>();
        productDetailsParamsList.add(productDetailsParams);

        String leftPayload = "";
        String rightPayload = "";

        // 以下对payload的处理是因为Gooble IAP v3.0不允许AccountId和ProfileId包含玩家信息，所以有必要做一些混淆，否则发起支付会失败
        if(!TextUtils.isEmpty(payload)) {
            try {
                byte[] data = payload.getBytes("UTF-8");
                String encoded = Base64.encodeToString(data, Base64.DEFAULT);
                if (encoded.length() > 0) {
                    leftPayload = getRandomPrefix() + encoded.substring(0, encoded.length()/2);
                    rightPayload = getRandomPrefix() + encoded.substring(encoded.length()/2);
                }
            } catch (UnsupportedEncodingException e) {
                Log.e(TAG, "Failed to encode payload for purchase flow", e);
            }
        }

        BillingFlowParams billingFlowParams = BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(productDetailsParamsList)
                .setObfuscatedAccountId(leftPayload)
                .setObfuscatedProfileId(rightPayload)
                .build();

        BillingResult result = billingClient.launchBillingFlow(activity, billingFlowParams);
        Log.d(TAG, "launchPurchaseFlow result = " + result.getResponseCode());
    }

    //补单
    private void checkUnDeliveredOrders2() {
//        Log.d(TAG, "检查补单");
        billingClient.queryPurchasesAsync(
                QueryPurchasesParams.newBuilder()
                        .setProductType(BillingClient.ProductType.INAPP)
                        .build(),
                new PurchasesResponseListener() {
                    @Override
                    public void onQueryPurchasesResponse(@NonNull BillingResult billingResult,
                                                         @NonNull List<Purchase> purchases) {
                        // 处理未发货订单逻辑保持不变
                        if((billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) && (purchases != null)) {
                            for(Purchase purchase : purchases) {
                                if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                                    callBackWithPurchaseData(purchase, true, false);
                                }
                            }
                        }
                        listener.onUndeliveredOrder(SDKType.GOOGLE_PAY,
                                ErrorCode.NO_MORE_UNDELIVERED_ORDER,
                                "",
                                "",
                                "",
                                "",
                                null,
                                0);
                    }

                }
        );
    }

    private Boolean isNewVersion(){
        return isNewVersion;
    }

    @Override
    public void setNewVersion(String isNew){
        Log.d(TAG, "isNew:" + isNew);
        isNewVersion = (isNew != null && !isNew.isEmpty() && isNew.equals("new"));
    }

    public boolean isFeatureSupported() {
        if (billingSupportedStatus == 1) {
            return true;
        }
        else if (billingSupportedStatus == 2) {
            return false;
        }
        else {
            if (billingClient != null) {
                try {
                    BillingResult billingResult = billingClient.isFeatureSupported(BillingClient.FeatureType.PRODUCT_DETAILS);
                    int responseCode = billingResult.getResponseCode();
                    switch (responseCode) {
                        case BillingClient.BillingResponseCode.OK:
                            billingSupportedStatus = 1;
                            break;
                        case BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED:
                            billingSupportedStatus = 2;
                            break;
                        default:
                            billingSupportedStatus = 3;
                            break;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Failed to check feature status", e);
                }
            }
        }
        Log.d(TAG, "billingSupportedStatus:" + billingSupportedStatus);
        return (billingSupportedStatus != 2);
    }

    public void getBillingConfigAsync(){
        GetBillingConfigParams getBillingConfigParams = GetBillingConfigParams.newBuilder().build();
        billingClient.getBillingConfigAsync(getBillingConfigParams,
                new BillingConfigResponseListener() {
                    public void onBillingConfigResponse(
                            BillingResult billingResult, BillingConfig billingConfig) {
                        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK
                                && billingConfig != null) {
                            String countryCode = billingConfig.getCountryCode();
                            m_countryCode = countryCode;
                        } else {
                            m_countryCode = "error:"+billingResult.getResponseCode();
                        }
                        Log.d(TAG, "countryCode:" + m_countryCode);
                    }
                });
    }

    public String getBillingConfig(){
        return (m_countryCode!=null?m_countryCode:"");
    }

}
