package com.frontier.admodsdk;

import android.app.Activity;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.frontier.sdkbase.AbstractAdImpl;
import com.frontier.sdkbase.AdEvent;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKManager;
import com.frontier.sdkbase.SDKType;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

public class AdmodHelper extends AbstractAdImpl {
    private static final String TAG = "AdmodHelper";

    public AdmodHelper(Activity activity, EventListener listener) {
        super(activity, listener);

    }

    @Override
    public void initSDK() {
        MobileAds.initialize(activity, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(@NonNull InitializationStatus initializationStatus) {
                Log.d(TAG, "AdMobSDK initialize.");
            }
        });
    }

    @Override
    public void addCustomProperties(JSONObject json) {
//        LevelPlaySegment segment = new LevelPlaySegment();
//        for (Iterator<String> it = json.keys(); it.hasNext(); ) {
//            String key = it.next();
//            try {
//                String value = String.valueOf(json.get(key));
//                segment.setCustom(key, value);
//            } catch (JSONException e) {
//                throw new RuntimeException(e);
//            }
//        }
//        addCustomPropertiesTask = new Runnable() {
//            @Override
//            public void run() {
//                LevelPlay.setSegment(segment);
//            }
//        };
//        if(inited) {
//            addCustomPropertiesTask.run();
//            addCustomPropertiesTask = null;
//        }
    }

    @Override
    public void addCustomSegments(JSONObject json) {
//        LevelPlaySegment segment = new LevelPlaySegment();
//        for (Iterator<String> it = json.keys(); it.hasNext(); ) {
//            String key = it.next();
//            Log.d(TAG, "key=" + key);
//            segment.setSegmentName(key);
//        }
//        addCustomPropertiesTask = new Runnable() {
//            @Override
//            public void run() {
//                LevelPlay.setSegment(segment);
//            }
//        };
//        if(inited) {
//            addCustomPropertiesTask.run();
//            addCustomPropertiesTask = null;
//        }
    }

    @Override
    public int sdkType() {
        return SDKType.ADMOB;
    }

    @Override
    public boolean isSupportSDKType(int sdkType) {
        return sdkType == sdkType();
    }

    @Override
    public void loadRewardedVideo() {
//        if (mRewardedAd != null) {
//            mRewardedAd.loadAd();
//        }
        listener.onAdEvent(sdkType(), AdEvent.REWARDEDVIDEO_AD_LOAD_START, null, -1);
    }

    @Override
    public void loadInterstitialVideo() {
//        if (mInterstitialAd != null) {
//            mInterstitialAd.loadAd();
//        }
        listener.onAdEvent(sdkType(), AdEvent.INTERSTITIAL_AD_LOAD_START, null, -1);
    }

    @Override
    public boolean showRewardedVideo(String placementName) {
//        if (mRewardedAd != null && mRewardedAd.isAdReady()) {
//            if(TextUtils.isEmpty(placementName)) {
//                mRewardedAd.showAd(activity);
//            } else if (!LevelPlayRewardedAd.isPlacementCapped(placementName)) {
//                mRewardedAd.showAd(activity, placementName);
//            }
//            return true;
//        }
        return false;
    }

    @Override
    public boolean isRewardedVideoReady() {
//        return mRewardedAd != null && mRewardedAd.isAdReady();
        return false;
    }

    @Override
    public boolean isInterstitialVideoReady() {
//        long current = System.currentTimeMillis();
//        if(current - showRewardVideoTS < showInterstitialTimeSpan) {
//            return false;
//        }
//        return mInterstitialAd != null && mInterstitialAd.isAdReady();
        return false;
    }

    @Override
    public boolean showInterstitial(String placementName) {
//        if(mInterstitialAd != null && mInterstitialAd.isAdReady()) {
//            if(TextUtils.isEmpty(placementName)) {
//                mInterstitialAd.showAd(activity);
//            } else if (!LevelPlayInterstitialAd.isPlacementCapped(placementName)){
//                mInterstitialAd.showAd(activity, placementName);
//            }
//            return true;
//        }

        return false;
    }

    @Override
    public void uploadPlayerData(JSONObject json) {
        if(json.has("uid")) {
//            userID = json.optString("uid", "");
        }
    }

    @Override
    public void onPause() {

    }

    @Override
    public void onResume() {

    }
}
