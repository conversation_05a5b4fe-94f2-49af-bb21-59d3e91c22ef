plugins {
    id 'com.android.library'
}

android {
    namespace "com.frontier.googlenpgaccountsdk"
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

repositories {
    maven { url("play_services_games_v2_16.0.1-eap/") }
}

dependencies {
    implementation project(':sdkbase')
    implementation "com.google.android.gms:play-services-games-v2:+"
    implementation files('libs/inputmapping-0.0.4.aar')
}