package com.frontier.googlenpgaccountsdk;

import android.view.KeyEvent;

import androidx.annotation.NonNull;

import com.google.android.libraries.play.games.inputmapping.InputMappingProvider;
import com.google.android.libraries.play.games.inputmapping.datamodel.InputAction;
import com.google.android.libraries.play.games.inputmapping.datamodel.InputControls;
import com.google.android.libraries.play.games.inputmapping.datamodel.InputGroup;
import com.google.android.libraries.play.games.inputmapping.datamodel.InputMap;
import com.google.android.libraries.play.games.inputmapping.datamodel.MouseSettings;

import java.util.ArrayList;
import java.util.Arrays;

public class KeyInputMapProvider extends InputMappingProvider {
    private enum InputEventIds {
        SPACE
    }

    @NonNull
    @Override
    public InputMap onProvideInputMap() {
        InputAction spaceInputAction = InputAction.create(
                "Spin",
                InputEventIds.SPACE.ordinal(),
                InputControls.create(
                        Arrays.asList(KeyEvent.KEYCODE_SPACE),
                        new ArrayList<>()
                )
        );
        InputGroup spaceInputGroup = InputGroup.create("Play",
                Arrays.asList(spaceInputAction));

        return InputMap.create(
                Arrays.asList(spaceInputGroup),
                MouseSettings.create(true, true)
        );
    }
}
