package com.frontier.googlenpgaccountsdk;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;

import com.frontier.sdkbase.AbstractAccountImpl;
import com.frontier.sdkbase.ErrorCode;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKType;
import com.google.android.gms.games.AuthenticationResult;
import com.google.android.gms.games.GamesSignInClient;
import com.google.android.gms.games.PlayGames;
import com.google.android.gms.games.Player;
import com.google.android.gms.games.PlayersClient;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.libraries.play.games.inputmapping.Input;
import com.google.android.libraries.play.games.inputmapping.InputMappingClient;
import com.google.android.gms.games.PlayGamesSdk;

// google 账号登录
public class GoogleNPGAccountHelper extends AbstractAccountImpl {
    private static final String TAG = "GoogleNPGAccountHelper";
    private static final String OAUTH_CLIENT_ID = "************-5f0vd5cd4m991qa6af3u29mkgc85inf8.apps.googleusercontent.com";
    private Boolean isLogin = false;
    private Player currentPlayer;
    public GoogleNPGAccountHelper(Activity activity, EventListener listener) {
        super(activity, listener);

        InputMappingClient inputMappingClient = Input.getInputMappingClient(activity);
        inputMappingClient.registerInputMappingProvider(new KeyInputMapProvider());

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            PlayGamesSdk.initialize(activity.getApplicationContext());
        }
    }

    private void onGetPlayerInfo(Task<Player> task) {
        if(task.isSuccessful()) {
            Player player = task.getResult();
            if(player != null) {
                currentPlayer = player;
                Log.d(TAG, player.getDisplayName());
                Log.d(TAG, player.getPlayerId());
                Log.d(TAG, player.getTitle());
                if(player.getIconImageUri() != null) {
                    Log.d(TAG, player.getIconImageUri().toString());
                }
                onLoginSuccess();
            } else {
                onLoginFailed(ErrorCode.COMMON_ERROR);
            }
        } else {
            onLoginFailed(ErrorCode.COMMON_ERROR);
        }
    }

    private void requestToken(GamesSignInClient gamesSignInClient) {
        gamesSignInClient.requestServerSideAccess(OAUTH_CLIENT_ID, true)
                .addOnCompleteListener(new OnCompleteListener<String>() {
                    @Override
                    public void onComplete(Task<String> task) {
                        if(task.isSuccessful()) {
                            String serverAuthToken = task.getResult();
                            Log.d(TAG, "serverAuthToken = " + serverAuthToken);
                            onLoginFailed(ErrorCode.SDK_NOT_LOGIN);
                        } else {
                            onLoginFailed(ErrorCode.SDK_NOT_LOGIN);
                        }
                    }
                });
    }

    private void launchLogin(@NonNull GamesSignInClient gamesSignInClient) {
        gamesSignInClient.signIn().addOnCompleteListener(new OnCompleteListener<AuthenticationResult>() {
            @Override
            public void onComplete(Task<AuthenticationResult> task) {
                if(task.isSuccessful()) {
                    PlayersClient playersClient = PlayGames.getPlayersClient(activity);
                    playersClient.getCurrentPlayer().addOnCompleteListener(new OnCompleteListener<Player>() {
                        @Override
                        public void onComplete(Task<Player> task) {
                            onGetPlayerInfo(task);
                        }
                    });
//                    requestToken(gamesSignInClient);
                } else {
                    onLoginFailed(ErrorCode.SDK_NOT_LOGIN);
                }
            }
        });
    }

    @Override
    public void login(int sdkType) {
        if(isLogin) {
            onLoginSuccess();
        } else {
            GamesSignInClient gamesSignInClient = PlayGames.getGamesSignInClient(activity);
            gamesSignInClient.isAuthenticated().addOnCompleteListener(new OnCompleteListener<AuthenticationResult>() {
                @Override
                public void onComplete(Task<AuthenticationResult> task) {
                    if(!task.isSuccessful()) {
                        onLoginFailed(ErrorCode.COMMON_ERROR);
                        return;
                    }
                    if(!task.getResult().isAuthenticated()) {
                        launchLogin(gamesSignInClient);
                        return;
                    }
//                    requestToken(gamesSignInClient);
                    PlayersClient playersClient = PlayGames.getPlayersClient(activity);
                    playersClient.getCurrentPlayer().addOnCompleteListener(new OnCompleteListener<Player>() {
                        @Override
                        public void onComplete(Task<Player> task) {
                            onGetPlayerInfo(task);
                        }
                    });
                }
            });
        }
    }

    @Override
    public void logout(int sdkType) {
        isLogin = false;
        currentPlayer = null;
    }

    @Override
    public boolean isLogin() {
        return isLogin;
    }

    @Override
    public String getID() {
        if(currentPlayer != null) {
            return currentPlayer.getPlayerId();
        }
        return "";
    }

    @Override
    public String getName() {
        if(currentPlayer != null) {
            return currentPlayer.getDisplayName();
        }
        return "";
    }

    @Override
    public String getToken() {
        return "";
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {

    }

    @Override
    public int sdkType() {
        return SDKType.GOOGLE;
    }

    @Override
    public boolean isSupportSDKType(int sdkType) {
        return sdkType() == sdkType;
    }

    private void onLoginSuccess() {
        isLogin = true;
        String icon = "";
        if(currentPlayer.hasIconImage()) {
            icon = currentPlayer.getIconImageUri().toString();
        }
        listener.onLogin(sdkType(), ErrorCode.NO_ERROR, currentPlayer.getPlayerId(), currentPlayer.getDisplayName(), "", icon, "", null, 0);
    }

    private void onLoginFailed(int code) {
        isLogin = false;
        listener.onLogin(sdkType(),
                code,
                "",
                "",
                "",
                "",
                "",
                null,
                0);
    }
}
