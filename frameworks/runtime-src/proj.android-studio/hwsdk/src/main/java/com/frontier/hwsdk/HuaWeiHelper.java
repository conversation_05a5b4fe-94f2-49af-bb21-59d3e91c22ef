package com.frontier.hwsdk;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentSender;
import android.text.TextUtils;
import android.util.Log;

import com.frontier.sdkbase.AbstractAccountPayImpl;
import com.frontier.sdkbase.ErrorCode;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKType;
import com.huawei.hmf.tasks.OnFailureListener;
import com.huawei.hmf.tasks.OnSuccessListener;
import com.huawei.hmf.tasks.Task;
import com.huawei.hms.common.ApiException;
import com.huawei.hms.iap.Iap;
import com.huawei.hms.iap.IapApiException;
import com.huawei.hms.iap.entity.ConsumeOwnedPurchaseReq;
import com.huawei.hms.iap.entity.ConsumeOwnedPurchaseResult;
import com.huawei.hms.iap.entity.InAppPurchaseData;
import com.huawei.hms.iap.entity.IsEnvReadyResult;
import com.huawei.hms.iap.entity.OrderStatusCode;
import com.huawei.hms.iap.entity.OwnedPurchasesReq;
import com.huawei.hms.iap.entity.OwnedPurchasesResult;
import com.huawei.hms.iap.entity.ProductInfo;
import com.huawei.hms.iap.entity.ProductInfoReq;
import com.huawei.hms.iap.entity.ProductInfoResult;
import com.huawei.hms.iap.entity.PurchaseIntentReq;
import com.huawei.hms.iap.entity.PurchaseIntentResult;
import com.huawei.hms.iap.entity.PurchaseResultInfo;
import com.huawei.hms.jos.JosApps;
import com.huawei.hms.jos.JosAppsClient;
import com.huawei.hms.jos.games.Games;
import com.huawei.hms.jos.games.PlayersClient;
import com.huawei.hms.jos.games.player.Player;
import com.huawei.hms.support.account.AccountAuthManager;
import com.huawei.hms.support.account.request.AccountAuthParams;
import com.huawei.hms.support.account.request.AccountAuthParamsHelper;
import com.huawei.hms.support.account.result.AuthAccount;
import com.huawei.hms.support.account.service.AccountAuthService;
import com.huawei.hms.support.api.client.Status;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HuaWeiHelper extends AbstractAccountPayImpl {

    private static final int SIGN_CODE = 17835;
    private static final int PURCHASE_CODE = 24551;
    private static final String TAG = "HuaWeiHelper";
    private AccountAuthService service;
    private boolean envReadyQueryFinished = false;

    private Map<String, ProductInfo> productInfos;
    private Map<String, InAppPurchaseData> purchaseDatas;
    private String skuList;

    public HuaWeiHelper(Activity activity, EventListener listener) {
        super(activity, listener);
        purchaseDatas = new HashMap<>();
        productInfos = new HashMap<>();
        JosAppsClient appsClient = JosApps.getJosAppsClient(activity, null);
        appsClient.init();
        AccountAuthParamsHelper accountAuthParamsHelper = new AccountAuthParamsHelper(AccountAuthParams.DEFAULT_AUTH_REQUEST_PARAM);
        service = AccountAuthManager.getService(activity, accountAuthParamsHelper.setAuthorizationCode().setProfile()
                .setIdToken()
                .createParams(), 2);

        initPurchaseEnv(true);
    }

    private void initPurchaseEnv(final boolean isInit) {
        Task<IsEnvReadyResult> task = Iap.getIapClient(activity).isEnvReady();
        task.addOnSuccessListener(new OnSuccessListener<IsEnvReadyResult>() {
            @Override
            public void onSuccess(IsEnvReadyResult isEnvReadyResult) {
                envReadyQueryFinished = true;
                if(!TextUtils.isEmpty(skuList)) {
                    String tmp = skuList;
                    skuList = null;
                    querySkuDetails(tmp);
                }
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "isEnvReady failed, exception = " + e.getMessage());
                envReadyQueryFinished = false;
                if(e instanceof IapApiException) {
                    IapApiException exception = (IapApiException)e;
                    int statusCode = exception.getStatus().getStatusCode();
                    Log.e(TAG, "isEnvReady failed with code = " + statusCode);
                    if(statusCode == OrderStatusCode.ORDER_HWID_NOT_LOGIN) {
                        login(sdkType());
                    } else {
                        if(!isInit) {
                            AlertDialog.Builder builder = new AlertDialog.Builder(activity, android.R.style.Theme_Material_Dialog_Alert);
                            builder.setCancelable(true)
                                    .setTitle(R.string.sdk_not_inited_title)
                                    .setMessage(e.getLocalizedMessage())
                                    .setPositiveButton(R.string.confirm_txt, new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {

                                        }
                                    });
                            builder.create().show();
                        }
                    }
                }
            }
        });
    }

    private void onGetAuthSuccess(final AuthAccount authAccount) {
        Log.d(TAG, "getIdToken:" + authAccount.getIdToken());
        Log.d(TAG, "getUnionId:" + authAccount.getUnionId());
        Log.d(TAG, "getAvatarUri:" + authAccount.getAvatarUri());
        Log.d(TAG, "User Name = " + authAccount.getDisplayName());
        PlayersClient playersClient = Games.getPlayersClient(activity);
        Task<Player> playerTask = playersClient.getCurrentPlayer();
        playerTask.addOnSuccessListener(new OnSuccessListener<Player>() {
            @Override
            public void onSuccess(Player player) {
                envReadyQueryFinished = true;
                if(!TextUtils.isEmpty(skuList)) {
                    String tmp = skuList;
                    skuList = null;
                    querySkuDetails(tmp);
                }
                id = player.getPlayerId();
                name = player.getDisplayName();
                token = authAccount.getAccessToken();
                icon = authAccount.getAvatarUriString();
                email = authAccount.getEmail();
                isLogin = true;
                listener.onLogin(sdkType(), ErrorCode.NO_ERROR, id, name, token, icon, email, null, 0);
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "get player info failed, error = " + e.getMessage());
                listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, 0);
            }
        });
    }

    @Override
    public void login(int sdkType) {
        Log.d(TAG, "login, sdkType = " + sdkType);
        if(isLogin) {
            listener.onLogin(sdkType(), ErrorCode.NO_ERROR, id, name, token, icon, email, null, 0);
        } else {
            Task<AuthAccount> task = service.silentSignIn();
            task.addOnSuccessListener(new OnSuccessListener<AuthAccount>() {
                @Override
                public void onSuccess(AuthAccount authAccount) {
                    onGetAuthSuccess(authAccount);
                }
            });
            task.addOnFailureListener(new OnFailureListener() {
                @Override
                public void onFailure(Exception e) {
                    Log.e(TAG, "login failed, error = " + e.getMessage());
                    if(e instanceof ApiException) {
                        activity.startActivityForResult(service.getSignInIntent(), SIGN_CODE);
                    } else {
                        listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, 0);
                    }
                }
            });
        }
    }

    @Override
    public void logout(int sdkType) {
        if(isLogin) {
            service.signOut();
            id = "";
            name = "";
            token = "";
            icon = null;
            email = null;
            isLogin = false;
        }
    }

    @Override
    public String getID() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getToken() {
        return token;
    }

    @Override
    public int sdkType() {
        return SDKType.HUAWEI;
    }

    @Override
    public void purchase(final String itemId, final String payload, final JSONObject jsonData) {
        if(!envReadyQueryFinished) {
            initPurchaseEnv(false);
            listener.onPurchase(sdkType(), ErrorCode.SDK_NOT_INITED, "", "", "", "", null, 0);
            return;
        } else {
            if(productInfos.containsKey(itemId)) {
                queryUndeliveredPurchaseAndLaunch(itemId, payload, jsonData);
            } else {
                List<String> itemIdList = new ArrayList<>();
                itemIdList.add(itemId);
                ProductInfoReq req = new ProductInfoReq();
                req.setPriceType(0);
                req.setProductIds(itemIdList);
                Task<ProductInfoResult> task = Iap.getIapClient(activity).obtainProductInfo(req);
                task.addOnSuccessListener(new OnSuccessListener<ProductInfoResult>() {
                    @Override
                    public void onSuccess(ProductInfoResult productInfoResult) {
                        List<ProductInfo> productList = productInfoResult.getProductInfoList();
                        for(ProductInfo productInfo : productList) {
                            productInfos.put(itemId, productInfo);
                        }
                        queryUndeliveredPurchaseAndLaunch(itemId, payload, jsonData);
                    }
                }).addOnFailureListener(new OnFailureListener() {
                    @Override
                    public void onFailure(Exception e) {
                        Log.e(TAG, "failed to get product info, error = " + e.getMessage());
                        // back to lua for failed to get product info
                        listener.onPurchase(SDKType.HUAWEI_PAY, ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                    }
                });
            }
        }
    }

    @Override
    public void querySkuDetails(String skuList) {
        if(envReadyQueryFinished) {
            String[] skuListArray = skuList.split(",");
            final List<String> list = java.util.Arrays.asList(skuListArray);
            ProductInfoReq req = new ProductInfoReq();
            req.setPriceType(0);
            req.setProductIds(list);
            Task<ProductInfoResult> task = Iap.getIapClient(activity).obtainProductInfo(req);
            task.addOnSuccessListener(new OnSuccessListener<ProductInfoResult>() {
                @Override
                public void onSuccess(ProductInfoResult productInfoResult) {
                    JSONArray skuArray = new JSONArray();
                    List<ProductInfo> productList = productInfoResult.getProductInfoList();
                    for(ProductInfo productInfo : productList) {
                        String productId = productInfo.getProductId();
                        String title = productInfo.getProductName();
                        String description = productInfo.getProductDesc();
                        String price = productInfo.getPrice();
                        long priceAmountMicros = productInfo.getOriginalMicroPrice();
                        JSONObject json = new JSONObject();
                        try {
                            json.put("product_id", productId);
                            json.put("price", price);
                            json.put("description", description);
                            json.put("title", title);
                            json.put("price_amount_micros", priceAmountMicros);
                            skuArray.put(json);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        productInfos.put(productId, productInfo);
                    }
                    listener.onQuerySkuDetails(sdkType(), ErrorCode.NO_ERROR, skuArray, 0);
                }
            }).addOnFailureListener(new OnFailureListener() {
                @Override
                public void onFailure(Exception e) {
                    listener.onQuerySkuDetails(sdkType(), ErrorCode.COMMON_ERROR, null, 0);
                }
            });
        } else {
            this.skuList = skuList;
        }
    }

    // 这个接口是在游戏启动时调用的，用来检查补单
    @Override
    public void checkUnDeliveredOrders() {
        OwnedPurchasesReq ownedPurchasesReq = new OwnedPurchasesReq();
        ownedPurchasesReq.setPriceType(0);
        Task<OwnedPurchasesResult> task = Iap.getIapClient(activity).obtainOwnedPurchases(ownedPurchasesReq);
        task.addOnSuccessListener(new OnSuccessListener<OwnedPurchasesResult>() {
            @Override
            public void onSuccess(OwnedPurchasesResult ownedPurchasesResult) {
                List<String> undeliveredOrders = ownedPurchasesResult.getInAppPurchaseDataList();
                List<String> signatures = ownedPurchasesResult.getInAppSignature();

                // 这里将所有的丢单数据一笔一笔的返回给lua端
                for(int i = 0; i < undeliveredOrders.size(); ++ i) {
                    try {
                        InAppPurchaseData data = new InAppPurchaseData(undeliveredOrders.get(i));
                        callBackWithPurchaseData(data, signatures.get(i), true);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }

                // 所有丢单数据传回到lua完成之后，最后告诉lua端，丢单数据已经传送完毕
                listener.onUndeliveredOrder(SDKType.HUAWEI_PAY,
                        ErrorCode.NO_MORE_UNDELIVERED_ORDER,
                        "",
                        "",
                        "",
                        "",
                        null,
                        0);
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(Exception e) {

            }
        });
    }

    @Override
    public void consumePurchase(final String orderId) {
        if(!TextUtils.isEmpty(orderId) && purchaseDatas.containsKey(orderId)) {
            InAppPurchaseData purchaseData = purchaseDatas.get(orderId);
            ConsumeOwnedPurchaseReq req = new ConsumeOwnedPurchaseReq();
            req.setPurchaseToken(purchaseData.getPurchaseToken());
            Task<ConsumeOwnedPurchaseResult> task = Iap.getIapClient(activity).consumeOwnedPurchase(req);
            task.addOnSuccessListener(new OnSuccessListener<ConsumeOwnedPurchaseResult>() {
                @Override
                public void onSuccess(ConsumeOwnedPurchaseResult consumeOwnedPurchaseResult) {
                    Log.d(TAG, orderId + " consumed successfully.");
                }
            }).addOnFailureListener(new OnFailureListener() {
                @Override
                public void onFailure(Exception e) {
                    Log.d(TAG, orderId + " consumed failed with error = " + e.getMessage());
                }
            });
            purchaseDatas.remove(orderId);
        }
    }

    private void queryUndeliveredPurchaseAndLaunch(final String itemId, final String payload, final JSONObject jsonData) {
        OwnedPurchasesReq ownedPurchasesReq = new OwnedPurchasesReq();
        ownedPurchasesReq.setPriceType(0);
        Task<OwnedPurchasesResult> task = Iap.getIapClient(activity).obtainOwnedPurchases(ownedPurchasesReq);
        task.addOnSuccessListener(new OnSuccessListener<OwnedPurchasesResult>() {
            @Override
            public void onSuccess(final OwnedPurchasesResult ownedPurchasesResult) {
                if(ownedPurchasesResult != null && ownedPurchasesResult.getInAppPurchaseDataList() != null) {
                    for(int i = 0; i < ownedPurchasesResult.getInAppPurchaseDataList().size(); ++ i) {
                        String inApppurchaseData = ownedPurchasesResult.getInAppPurchaseDataList().get(i);
                        try {
                            final InAppPurchaseData purchaseData = new InAppPurchaseData(inApppurchaseData);
                            final String signature = ownedPurchasesResult.getInAppSignature().get(i);
                            if(itemId.equals(purchaseData.getProductId())) {
                                // 有itemId对应的丢单，需要进行补单
                                AlertDialog.Builder builder = new AlertDialog.Builder(activity, android.R.style.Theme_Material_Dialog_Alert);
                                builder.setMessage(R.string.restore_order_tip)
                                        .setCancelable(false)
                                        .setPositiveButton(R.string.confirm_txt, new DialogInterface.OnClickListener() {
                                            @Override
                                            public void onClick(DialogInterface dialog, int which) {
                                                // 这里的补单是玩家再次点击相同的商品发起的，所以replenishment置为false
                                                // 只有游戏启动时的自动检测补单replenishment置为true
                                                callBackWithPurchaseData(purchaseData, signature, false);
                                            }
                                        });
                                builder.create().show();
                                return;
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                }
                launchPurchaseFlow(itemId, payload, jsonData);
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(Exception e) {
                launchPurchaseFlow(itemId, payload, jsonData);
            }
        });
    }

    private void launchPurchaseFlow(String itemId, String payload, JSONObject jsonData) {
        Log.d(TAG, "launchPurchaseFlow log 1");
        PurchaseIntentReq req = new PurchaseIntentReq();
        req.setProductId(itemId);
        req.setPriceType(0);
        if(!TextUtils.isEmpty(payload)) {
            req.setDeveloperPayload(payload);
        }
        Task<PurchaseIntentResult> task = Iap.getIapClient(activity).createPurchaseIntent(req);
        task.addOnSuccessListener(new OnSuccessListener<PurchaseIntentResult>() {
            @Override
            public void onSuccess(PurchaseIntentResult purchaseIntentResult) {
                Log.d(TAG, "launchPurchaseFlow log 2");
                Status status = purchaseIntentResult.getStatus();
                if(status.hasResolution()) {
                    Log.d(TAG, "launchPurchaseFlow log 3");
                    try {
                        status.startResolutionForResult(activity, PURCHASE_CODE);
                    } catch (IntentSender.SendIntentException e) {
                        Log.d(TAG, "launchPurchaseFlow log 4");
                        e.printStackTrace();
                        listener.onPurchase(SDKType.HUAWEI_PAY, ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                    }
                } else {
                    Log.d(TAG, "launchPurchaseFlow log 5");
                    listener.onPurchase(SDKType.HUAWEI_PAY, ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                }
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(Exception e) {
                e.printStackTrace();
                Log.d(TAG, "launchPurchaseFlow log 6");
                listener.onPurchase(SDKType.HUAWEI_PAY, ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
            }
        });
    }

    @Override
    public boolean isSupportSDKType(int sdkType) {
        return sdkType == SDKType.HUAWEI || sdkType == SDKType.HUAWEI_PAY;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        Log.d(TAG, "onActivityResult log 1");
        if(requestCode == SIGN_CODE) {
            Log.d(TAG, "onActivityResult log 2");
            Task<AuthAccount> authAccountTask = AccountAuthManager.parseAuthResultFromIntent(data);
            if(authAccountTask.isSuccessful()) {
                onGetAuthSuccess(authAccountTask.getResult());
            } else {
                Log.e(TAG, "login failed 2 error = " + authAccountTask.getException().getMessage());
                listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, 0);
            }
        } else if(requestCode == PURCHASE_CODE) {
            Log.e(TAG, "onActivityResult log 3");
            if(data == null) {
                Log.e(TAG, "onActivityResult log 4");
                listener.onPurchase(SDKType.HUAWEI_PAY, ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                return;
            }
            Log.e(TAG, "onActivityResult log 5");
            PurchaseResultInfo purchaseResultInfo = Iap.getIapClient(activity).parsePurchaseResultInfoFromIntent(data);
            switch(purchaseResultInfo.getReturnCode()) {
                case OrderStatusCode.ORDER_STATE_CANCEL:
                    listener.onPurchase(SDKType.HUAWEI_PAY, ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                    break;
                case OrderStatusCode.ORDER_STATE_FAILED:
                    listener.onPurchase(SDKType.HUAWEI_PAY, ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                    break;
                case OrderStatusCode.ORDER_PRODUCT_OWNED:
                    break;
                case OrderStatusCode.ORDER_STATE_SUCCESS:
                    String inAppPurchaseData = purchaseResultInfo.getInAppPurchaseData();
                    InAppPurchaseData purchaseData = null;
                    try {
                        purchaseData = new InAppPurchaseData(inAppPurchaseData);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    String inAppPurchaseDataSignature = purchaseResultInfo.getInAppDataSignature();
                    callBackWithPurchaseData(purchaseData, inAppPurchaseDataSignature, false);
                    break;
            }
        }
    }

    // 将订单数据返回给lua端
    // 只有游戏启动时的自动检测补单replenishment置为true
    private void callBackWithPurchaseData(InAppPurchaseData purchaseData, String inAppPurchaseDataSignature, boolean replenishment) {
        if(purchaseDatas.containsKey(purchaseData.getOrderID())) {
            purchaseDatas.remove(purchaseData.getOrderID());
        }
        purchaseDatas.put(purchaseData.getOrderID(), purchaseData);
        JSONObject json = new JSONObject();
        try {
            json.put("account_flag", purchaseData.getAccountFlag());
//            json.put("signature", inAppPurchaseDataSignature);
            if(replenishment) {
                listener.onUndeliveredOrder(SDKType.HUAWEI_PAY,
                        ErrorCode.NO_ERROR,
                        purchaseData.getProductId(),
                        purchaseData.getOrderID(),
                        purchaseData.getDeveloperPayload(),
                        purchaseData.getPurchaseToken(),
                        json,
                        0);
            } else {
                listener.onPurchase(SDKType.HUAWEI_PAY,
                        ErrorCode.NO_ERROR,
                        purchaseData.getProductId(),
                        purchaseData.getOrderID(),
                        purchaseData.getDeveloperPayload(),
                        purchaseData.getPurchaseToken(),
                        json,
                        0);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
