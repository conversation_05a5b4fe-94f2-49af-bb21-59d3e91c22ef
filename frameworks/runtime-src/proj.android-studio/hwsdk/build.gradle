apply plugin: 'com.android.library'

android {
    namespace "com.frontier.hwsdk"
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.huawei.agconnect:agconnect-core:1.4.1.300'
    implementation 'com.huawei.agconnect:agconnect-auth:1.4.1.300'
    implementation 'com.huawei.hms:base:5.0.3.300'
    implementation 'com.huawei.hms:hwid:5.0.3.301'
    implementation 'com.huawei.hms:game:5.0.3.301'
    implementation 'com.huawei.hms:iap:5.0.2.300'
    implementation project(path: ':sdkbase')

}