package com.frontier.sdkbase;

public class SDKType {
    // 以下为登陆sdk类型
    public static final int FACEBOOK = 1;
    public static final int SMS = 2;
    public static final int GAME_CENTER = 3;
    public static final int GUEST = 4;
    public static final int FB_LATER = 5;
    public static final int HUAWEI = 6;
    public static final int EMAIL = 7;
    public static final int MESSENGER = 8;
    public static final int LINE = 9;
    public static final int WHATSAPP = 10;
    public static final int GOOGLE = 11;
    public static final int INSTAGRAM = 12;
    public static final int MICROSOFT = 13;
    public static final int TWITTER = 14;
    public static final int SAMCLOUD = 15;

    // 以下为支付sdk类型
    public static final int APPLE_PAY = 1000;
    public static final int GOOGLE_PAY = 1001;
    public static final int AMAZON_PAY = 1002;
    public static final int HUAWEI_PAY = 1003;
    public static final int MICROSOFT_PAY = 1004;
    public static final int SAMSUNG_PAY = 1005;
    public static final int ONESTORE_PAY = 1006;

    public static final int IRONSOURCE = 2000;
    public static final int ADMOB = 2001;

    public static final int ADJUST = 3000;

    public static final int MORE_SDK = 4000;
    public static final int CLOUD_SDK = 4001;
    public static final int ODEEO = 5000;

;}
