package com.frontier.sdkbase;

import static androidx.core.app.ActivityCompat.requestPermissions;

import android.Manifest;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.usage.StorageStats;
import android.app.usage.StorageStatsManager;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.media.Ringtone;
import android.media.RingtoneManager;
import android.media.SoundPool;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.MessageQueue;
import android.os.SystemClock;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.widget.RemoteViews;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.core.content.pm.ShortcutInfoCompat;
import androidx.core.content.pm.ShortcutManagerCompat;
import androidx.core.graphics.drawable.IconCompat;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class SDKManager {
    private static final String TAG = "SDKManager";
    private static final int REQUEST_PERMISSION_CODE = 35632;
    private static Activity activity;
    private static String id; // 这个字段表示服务器发送的推送的id
    private static int localIdentifier;  // 这个字段表示客户端发送的本地推送的id
    private static int coinPresentationCount = 0;
    private static double dollarPresentationCount = 0.0;

    private static EventListener thisListener;

    private static ArrayList<AbstractAccountPayImpl> accountPaySDKs;
    private static ArrayList<AbstractPayImpl> paySDKs;
    private static ArrayList<AbstractAccountImpl> accountSDKs;
    private static ArrayList<AbstractAdImpl> adSDKs;
    private static ArrayList<AbstractMoreImpl> moreSDKs;
    private static ArrayList<AbstractAudioImpl> audioSDKs;

    private static int loginLuaCallback = -1;
    private static int logoutLuaCallback = -1;
    private static int purchaseLuaCallback = -1;
    private static int checkUndeliveredOrdersLuaCallback = -1;
    private static int querySkuDetailsLuaCallback = -1;
    private static int getStorageStatsLuaCallback = -1;
    private static int checkNotificationLuaCallback = -1;
    private static int requestNotificationLuaCallback = -1;
    private static int adEventLuaCallback = -1;
    private static int audioAdEventLuaCallback = -1;
    private static int createShortCutLuaCallback = -1;
    private static int shareLuaCallback = -1;
    private static SoundPool mSoundPool = null;

    private static Handler handler = new Handler();
    private static ArrayList<VibrateRunnable> vibrateRunnables = new ArrayList<>();
    private static HashMap<String, String> parameters = new HashMap<>();

    private static int refCount = 0;
    private static Runnable changeIconTask = null;
    private static int targetIconId = 0;
    private static String adjustADID = "";

    private static class VibrateRunnable implements Runnable {
        private boolean canceled = false;
        private long duration;
        public VibrateRunnable(long duration) {
            this.duration = duration;
        }
        @Override
        public void run() {
            if(!canceled) {
                Vibrator v = (Vibrator)activity.getSystemService(Context.VIBRATOR_SERVICE);
                if(v != null && duration > 0) {
                    try {
                        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            v.vibrate(VibrationEffect.createOneShot(duration, VibrationEffect.DEFAULT_AMPLITUDE));
                        } else {
                            v.vibrate(duration);
                        }
                    } catch(NullPointerException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        public void cancel() {
            canceled = true;
        }
    }

    public static void setAdjustADID(String adid) {
        adjustADID = adid;
    }

    public static String getAdjustADID() {
        return adjustADID;
    }

    public static void init(Activity activity, int accountPaySDKResId, int paySDKResId, int accountResId, int adResId, int moreResId, int audioResId, final EventListener listener) {
        SDKManager.activity = activity;
        Intent intent = activity.getIntent();
        initPresentationInfo(intent);
        initNotificationChannel();

        thisListener = new EventListener() {
            @Override
            public void loadSyncThreadAtStartApp(String key) {
                listener.loadSyncThreadAtStartApp(key);
            }
            @Override
            public void onLogin(int sdkType, int errorCode, String id, String name, String token, String icon, String email, JSONObject json, int param) {
                if(loginLuaCallback != -1) {
                    listener.onLogin(sdkType, errorCode, id, name, token, icon, email, json, loginLuaCallback);
                    loginLuaCallback = -1;
                }
            }

            @Override
            public void onLogout(int sdkType, int errorCode, JSONObject json, int param) {
                if(logoutLuaCallback != -1) {
                    listener.onLogout(sdkType, errorCode, json, logoutLuaCallback);
                    logoutLuaCallback = -1;
                }
            }

            @Override
            public void onPurchase(int sdkType, int errorCode, String productId, String orderId, String payload, String token, JSONObject json, int param) {
                if(purchaseLuaCallback != -1) {
                    listener.onPurchase(sdkType, errorCode, productId, orderId, payload, token, json, purchaseLuaCallback);
                    purchaseLuaCallback = -1;
                }
            }

            @Override
            public void onUndeliveredOrder(int sdkType, int errorCode, String productId, String orderId, String payload, String token, JSONObject json, int param) {
                if(checkUndeliveredOrdersLuaCallback != -1) {
                    listener.onUndeliveredOrder(sdkType, errorCode, productId, orderId, payload, token, json, checkUndeliveredOrdersLuaCallback);
                    if(errorCode != ErrorCode.NO_ERROR) {
                        checkUndeliveredOrdersLuaCallback = -1;
                    }
                }
            }

            @Override
            public void onQuerySkuDetails(int sdkType, int errorCode, JSONArray data, int param) {
                if(querySkuDetailsLuaCallback != -1) {
                    listener.onQuerySkuDetails(sdkType, errorCode, data, querySkuDetailsLuaCallback);
                    querySkuDetailsLuaCallback = -1;
                }
            }

            @Override
            public void onGetNotificationSound(String sound, int luaCallback) {
                listener.onGetNotificationSound(sound, luaCallback);
            }

            @Override
            public void onGameLaunchStatus(boolean status) {
                listener.onGameLaunchStatus(status);
            }

            @Override
            public void onGetStorageStats(long cachedBytes, long dataBytes, long appBytes, int param) {
                if(getStorageStatsLuaCallback != -1) {
                    listener.onGetStorageStats(cachedBytes, dataBytes, appBytes, getStorageStatsLuaCallback);
                    getStorageStatsLuaCallback = -1;
                }
            }

            @Override
            public void onCheckNotificationPermission2(boolean enabled, int param) {
                if(checkNotificationLuaCallback != -1) {
                    listener.onCheckNotificationPermission2(enabled, checkNotificationLuaCallback);
                    checkNotificationLuaCallback = -1;
                }
            }

            @Override
            public void onCheckNotificationPermission(boolean enabled, int param) {
                if(param != -1) {
                    listener.onCheckNotificationPermission(enabled, param);
                }
            }

            @Override
            public void onRequestNotificationPermission(boolean enabled, int parma) {
                if(requestNotificationLuaCallback != -1) {
                    listener.onRequestNotificationPermission(enabled, requestNotificationLuaCallback);
                    requestNotificationLuaCallback = -1;
                }
            }

            @Override
            public void onAdEvent(int sdkType, int eventCode, JSONObject data, int param) {
                if(adEventLuaCallback != -1) {
                    listener.onAdEvent(sdkType, eventCode, data, adEventLuaCallback);
                }
            }

            @Override
            public void onAudioAdEvent(int sdkType, int eventCode, JSONObject data, int param) {
                if(audioAdEventLuaCallback != -1) {
                    if (AdEvent.INIT_SDK_FINISHED == eventCode) {
                        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
                        if(sdk != null) {
                            sdk.setExtendedUserId("adjust", adjustADID);
                        }
                    }
                    listener.onAudioAdEvent(sdkType, eventCode, data, audioAdEventLuaCallback);
                }
            }

            @Override
            public void onCreateShortCut(boolean enabled, int param){
                if(createShortCutLuaCallback != -1){
                    listener.onCreateShortCut(enabled, createShortCutLuaCallback);
                    createShortCutLuaCallback = -1;
                }
            }

            @Override
            public void onAddAPMTag(String key, String value) {
                listener.onAddAPMTag(key, value);
            }

            @Override
            public void onRemoveAPMTag(String key) {
                listener.onRemoveAPMTag(key);
            }

            @Override
            public void onSetUserIdToAPM(String userId) {
                listener.onSetUserIdToAPM(userId);
            }

            @Override
            public void onShareEvent(boolean enabled, String msg, int param){
                if(shareLuaCallback != -1){
                    listener.onShareEvent(enabled, msg, shareLuaCallback);
                    shareLuaCallback = -1;
                }
            }
        };
        accountPaySDKs = new ArrayList<>();
        paySDKs = new ArrayList<>();
        accountSDKs = new ArrayList<>();
        adSDKs = new ArrayList<>();
        moreSDKs = new ArrayList<>();
        audioSDKs = new ArrayList<>();
        initSDKs(accountPaySDKResId, paySDKResId, accountResId, adResId, moreResId, audioResId);
    }

    public static boolean isAndroidPC() {
        PackageManager pm = activity.getPackageManager();
        return pm.hasSystemFeature("org.chromium.arc");
    }

    public static boolean isInForeground(Context context) {
        final ActivityManager activityManager = (ActivityManager)context.getSystemService(Context.ACTIVITY_SERVICE);
        final List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
        if(runningProcesses == null) {
            return false;
        }
        for(final ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
            if(processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                for(final String activeProcess : processInfo.pkgList) {
                    Log.d(TAG, "activeProcess = " + activeProcess);
                    if(activeProcess.equals(context.getPackageName())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public static boolean isInForeground2(Context context) {
        ActivityManager activityManager = (ActivityManager)context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> taskInfos = activityManager.getRunningTasks(1);
        if(taskInfos == null) {
            return false;
        }
        if(taskInfos.isEmpty()) {
            return false;
        }
        ActivityManager.RunningTaskInfo runningTaskInfo = taskInfos.get(0);
        if(runningTaskInfo == null) {
            return false;
        }
        String s1 = context.getPackageName();
        String s2 = runningTaskInfo.baseActivity.getPackageName();
        String s3 = runningTaskInfo.topActivity.getPackageName();
        boolean result = context.getPackageName().equals(runningTaskInfo.topActivity.getPackageName());
        result |= context.getPackageName().equals(runningTaskInfo.baseActivity.getPackageName());
        return result;
    }

    private static ICommon constructSDK(String sdkClassName) {
        try {
            Class<? extends ICommon> clazz = (Class<? extends ICommon>) Class.forName(sdkClassName);
            Constructor<? extends ICommon> constructor = clazz.getConstructor(Activity.class, EventListener.class);
            return constructor.newInstance(activity, thisListener);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void initSDKs(int accountPaySDKResId, int paySDKResId, int accountSDKResId, int adSDKResId, int moreSDKResId, int audioResId) {
        String[] accountPaySDKNames = activity.getResources().getStringArray(accountPaySDKResId);
        Log.d("scene", "initSDKs_1");
        if(accountPaySDKs != null) {
            for(int i = 0; i < accountPaySDKNames.length; ++ i) {
                String sdkClassName = accountPaySDKNames[i];
                ICommon instance = constructSDK(sdkClassName);
                if(instance != null) {
                    accountPaySDKs.add((AbstractAccountPayImpl)instance);
                }
            }
        }
        Log.d("scene", "initSDKs_2");

        initSDK2Pay(paySDKResId);
        Log.d("scene", "initSDKs_3");

        initSDK2Account(accountSDKResId);
        Log.d("scene", "initSDKs_4");

        String[] adSDKNames = activity.getResources().getStringArray(adSDKResId);
        if(adSDKNames != null) {
            for(int i = 0; i < adSDKNames.length; ++i) {
                String sdkClassName = adSDKNames[i];
                ICommon instance = constructSDK(sdkClassName);
                if(instance != null) {
                    adSDKs.add((AbstractAdImpl)instance);
                }
            }
        }
        Log.d("scene", "initSDKs_5");

        String[] moreSDKNames = activity.getResources().getStringArray(moreSDKResId);
        if(moreSDKNames != null) {
            for(int i = 0; i < moreSDKNames.length; ++i) {
                String sdkClassName = moreSDKNames[i];
                ICommon instance = constructSDK(sdkClassName);
                if(instance != null) {
                    moreSDKs.add((AbstractMoreImpl)instance);
                }
            }
        }
        Log.d("scene", "initSDKs_6");

        String[] audioSDKNames = activity.getResources().getStringArray(audioResId);
        if(audioSDKNames != null) {
            for(int i = 0; i < audioSDKNames.length; ++i) {
                String sdkClassName = audioSDKNames[i];
                ICommon instance = constructSDK(sdkClassName);
                if(instance != null) {
                    audioSDKs.add((AbstractAudioImpl)instance);
                }
            }
        }
        Log.d("scene", "initSDKs_7");
    }

    private static void initSDK2Pay(int paySDKResId) {
        Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                String[] paySDKNames = activity.getResources().getStringArray(paySDKResId);
                if(paySDKNames != null) {
                    for(int i = 0; i < paySDKNames.length; ++ i) {
                        String sdkClassName = paySDKNames[i];
                        ICommon instance = constructSDK(sdkClassName);
                        if(instance != null) {
                            paySDKs.add((AbstractPayImpl)instance);
                        }
                    }
                }

                thisListener.loadSyncThreadAtStartApp("GooglePayHelper");
                return false;
            }
        });
    }

    private static void initSDK2Account(int accountSDKResId) {
        // 消耗65ms
        Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                String[] accountSDKNames = activity.getResources().getStringArray(accountSDKResId);
                if(accountSDKNames != null) {
                    for(int i = 0; i < accountSDKNames.length; ++i) {
                        String sdkClassName = accountSDKNames[i];
                        ICommon instance = constructSDK(sdkClassName);
                        if(instance != null) {
                            accountSDKs.add((AbstractAccountImpl)instance);
                        }
                    }
                }

                thisListener.loadSyncThreadAtStartApp("accountSDK");
                return false;
            }
        });
    }

    private static void initNotificationChannel() {
        // 消耗12ms
        Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                createNotificationChannel(activity, "Default", "");
                thisListener.loadSyncThreadAtStartApp("notificationChannel");
                return false;
            }
        });
    }

    public static IAccount findAccountSDK(int sdkType) {
        for(int i = 0; i < accountPaySDKs.size(); ++ i) {
            AbstractAccountPayImpl sdk = accountPaySDKs.get(i);
            if(sdk.isSupportSDKType(sdkType)) {
                return sdk;
            }
        }

        for(int i = 0; i < accountSDKs.size(); ++ i) {
            AbstractAccountImpl sdk = accountSDKs.get(i);
            if(sdk.isSupportSDKType(sdkType)) {
                return sdk;
            }
        }
        return null;
    }

    public static IPay findPaySDK(int sdkType) {
        for(int i = 0; i < accountPaySDKs.size(); ++ i) {
            AbstractAccountPayImpl sdk = accountPaySDKs.get(i);
            if(sdk.isSupportSDKType(sdkType)) {
                return sdk;
            }
        }

        for(int i = 0; i < paySDKs.size(); ++ i) {
            AbstractPayImpl sdk = paySDKs.get(i);
            if (sdk.isSupportSDKType(sdkType)) {
                return sdk;
            }
        }
        return null;
    }

    public static IAd findAdSDK(int sdkType) {
        for(int i = 0; i < adSDKs.size(); ++ i) {
            AbstractAdImpl sdk = adSDKs.get(i);
            if(sdk.isSupportSDKType(sdkType)) {
                return sdk;
            }
        }
        return null;
    }

    public static IMore findMoreSDK(int sdkType) {
        for(int i = 0; i < moreSDKs.size(); ++ i) {
            AbstractMoreImpl sdk = moreSDKs.get(i);
            if(sdk.isSupportSDKType(sdkType)) {
                return sdk;
            }
        }
        return null;
    }

    @Nullable
    public static IAudioAd findAudioAdSDK(int sdkType) {
        for(int i = 0; i < audioSDKs.size(); ++ i) {
            AbstractAudioImpl sdk = audioSDKs.get(i);
            if(sdk.isSupportSDKType(sdkType)) {
                return sdk;
            }
        }
        return null;
    }

    public static void setAdEventCallback(int luaCallback) {
        adEventLuaCallback = luaCallback;
    }

    public static void setAudioAdEventCallback(int luaCallback) {
        audioAdEventLuaCallback = luaCallback;
    }

    public static void initSDK(final int sdkType) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AbstractAccountImpl sdk = (AbstractAccountImpl) findAccountSDK(sdkType);
                if(sdk != null) {
                    sdk.initSDK();
                    return;
                }

                AbstractPayImpl sdk2 = (AbstractPayImpl) findPaySDK(sdkType);
                if(sdk2 != null) {
                    sdk2.initSDK();
                    return;
                }

                AbstractAdImpl sdk3 = (AbstractAdImpl) findAdSDK(sdkType);
                if(sdk3 != null) {
                    sdk3.initSDK();
                    return;
                }

                AbstractMoreImpl sdk4 = (AbstractMoreImpl) findMoreSDK(sdkType);
                if(sdk4 != null) {
                    sdk4.initSDK();
                    return;
                }

                AbstractAudioImpl sdk5 = (AbstractAudioImpl) findAudioAdSDK(sdkType);
                if(sdk5 != null) {
                    sdk5.initSDK();
                    return;
                }
            }
        });

    }

    public static void addCustomProperties(final int sdkType, final String properties) {
        final IAd sdk = findAdSDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        JSONObject json = new JSONObject(properties);
                        sdk.addCustomProperties(json);
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        }
    }

    public static void addCustomSegments(final int sdkType, final String properties) {
        final IAd sdk = findAdSDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        JSONObject json = new JSONObject(properties);
                        sdk.addCustomSegments(json);
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        }
    }

    public static void login(final int sdkType, int luaCallback) {
        loginLuaCallback = luaCallback;
        final IAccount sdk = findAccountSDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.login(sdkType);
                }
            });
        } else {
            thisListener.onLogin(sdkType, ErrorCode.SDK_NOT_FOUND, "","", "", "", "", null, 0);
        }
    }

    public static void logout(final int sdkType, int luaCallback) {
        logoutLuaCallback = luaCallback;
        final IAccount sdk = findAccountSDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.logout(sdkType);
                }
            });
        } else {
            thisListener.onLogout(sdkType, ErrorCode.SDK_NOT_FOUND, null, 0);
        }
    }

    public static boolean isSupportAccountSDK(int sdkType) {
        IAccount sdk = findAccountSDK(sdkType);
        return sdk != null;
    }

    public static boolean isSupportPaySDK(int sdkType) {
        IPay sdk = findPaySDK(sdkType);
        return sdk != null;
    }

    public static boolean isSupportAdSDK(int sdkType) {
        IAd sdk = findAdSDK(sdkType);
        return sdk != null;
    }

    public static boolean isSupportAudioAdSDK(int sdkType) {
        IAudioAd sdk = findAudioAdSDK(sdkType);
        return sdk != null;
    }

    public static void loadRewardedVideo(int sdkType) {
        final IAd sdk = findAdSDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.loadRewardedVideo();
                }
            });
        }
    }

    public static void loadInterstitialVideo(int sdkType) {
        final IAd sdk = findAdSDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.loadInterstitialVideo();
                }
            });
        }
    }

    public static boolean showRewardedVideo(int sdkType, final String placementName) {
        final IAd sdk = findAdSDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.showRewardedVideo(placementName);
                }
            });
        }
        return sdk != null;
    }

    public static boolean isRewardedVideoReady(int sdkType) {
        final IAd sdk = findAdSDK(sdkType);
        if(sdk != null) {
            return sdk.isRewardedVideoReady();
        }
        return false;
    }

    public static boolean isInterstitialVideoReady(int sdkType) {
        final IAd sdk = findAdSDK(sdkType);
        if(sdk != null) {
            return sdk.isInterstitialVideoReady();
        }
        return false;
    }

    public static boolean showInterstitial(int sdkType, final String placementName) {
        final IAd sdk = findAdSDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.showInterstitial(placementName);
                }
            });
        }
        return sdk != null;
    }

    // 音频SDK相关 begin

    public static boolean createAudioAdIconAd() {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.createIconAd();
                }
            });
        }
        return sdk != null;
    }

    public static boolean isAudioAdInitialized() {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            return sdk.isInitialized();
        }
        return false;
    }

    public static boolean isAudioAdAvailable(int adType) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            return sdk.isAdAvailable(adType);
        }
        return false;
    }

    public static boolean isAudioAdCached(int adType) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            return sdk.isAdCached(adType);
        }
        return false;
    }

    public static void showAudioAdIcon(int adType) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.showIcon(adType);
        }
    }

    public static float getAudioAdVolume() {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            return sdk.getVolume();
        }
        return 0;
    }

    public static void forceCloseAudioAd(int adType) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.forceClose(adType);
        }
    }

    public static void setAudioAdPosition(int xOffset, int yOffset, int adType) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.setPosition(xOffset, yOffset, adType);
        }
    }

    public static void setAudioAdSize(int size, int adType) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.setSize(size, adType);
        }
    }

    public static void setAudioAdColor(int color, int adType) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.setColor(color, adType);
        }
    }

    public static void setAudioAdOnlyAnimationColor(String mainColorHex, int adType) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.setAudioOnlyAnimationColor(mainColorHex, adType);
        }
    }

    public static void setAudioAdPublisherUserID(String uid) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.setPublisherUserID(uid);
        }
    }

    public static String getAudioAdPublisherUserID() {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            return sdk.getPublisherUserID();
        }
        return "";
    }

    public static void setAudioAdExtendedUserId(String partner, String uuid) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.setExtendedUserId(partner, uuid);
        }
    }

    public static void forceAudioAdRegulationTypeGDPR() {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.forceRegulationTypeGDPR();
        }
    }

    public static void forceAudioAdRegulationTypeCCPA() {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.forceRegulationTypeCCPA();
        }
    }

    public static void setAudioAdDoNotSellPrivacyString(String privacyString) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.setDoNotSellPrivacyString(privacyString);
        }
    }

    public static void setAudioAdIsChildDirected(boolean isChild) {
        final IAudioAd sdk = findAudioAdSDK(SDKType.ODEEO);
        if(sdk != null) {
            sdk.setIsChildDirected(isChild);
        }
    }

    // 音频SDK相关 end

    public static void systemShare(final String title, final String content, final String url) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Intent intent = new Intent(Intent.ACTION_SEND);
                intent.putExtra(Intent.EXTRA_TITLE, title);
                if(TextUtils.isEmpty(content)) {
                    intent.putExtra(Intent.EXTRA_TEXT, url);
                } else {
                    intent.putExtra(Intent.EXTRA_TEXT, content + "\n" + url);
                }

                intent.setType("text/plain");
                activity.startActivity(Intent.createChooser(intent, null));
            }
        });
    }

    public static void systemShareImage(String imagePath, String title, String text) {
        Uri image = FileProvider.getUriForFile(activity, "JackpotWorld", new File(imagePath));
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_SEND);
        intent.putExtra(Intent.EXTRA_STREAM, image);
        intent.setType("image/png");
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        activity.startActivity(Intent.createChooser(intent,title));
    }

    public static void purchase(int sdkType, final String itemId, final String payload, String jsonData, int luaCallback) {
        if(purchaseLuaCallback != -1) {
            Log.e(TAG, "already in purchase flow, sdkType = " + sdkType + ", itemId = " + itemId + ", payload = " + payload + ", jsonData = " + jsonData);
            return;
        }
        purchaseLuaCallback = luaCallback;
        final IPay sdk = findPaySDK(sdkType);
        if(sdk != null) {

            JSONObject json = null;
            if(!TextUtils.isEmpty(jsonData)) {
                try {
                    json = new JSONObject(jsonData);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            final JSONObject finalJson = json;
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.purchase(itemId, payload, finalJson);
                }
            });
        } else {
            thisListener.onPurchase(sdkType, ErrorCode.SDK_NOT_FOUND, "", "", "", "", null, 0);
        }
    }

    public static void querySkuDetails(int sdkType, final String skuList, int luaCallback) {
        querySkuDetailsLuaCallback = luaCallback;
        final IPay sdk = findPaySDK(sdkType);
        if(sdk != null) {
            sdk.querySkuDetails(skuList);
        } else {
            thisListener.onQuerySkuDetails(sdkType, ErrorCode.SDK_NOT_FOUND, null, luaCallback);
            querySkuDetailsLuaCallback = -1;
        }
    }

    public static void consumePurchase(int sdkType, final String orderId) {
        final IPay sdk = findPaySDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.consumePurchase(orderId);
                }
            });
        }
    }

    public static void checkUnDeliveredOrders(int sdkType, int luaCallback) {
        checkUndeliveredOrdersLuaCallback = luaCallback;
        final IPay sdk = findPaySDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.checkUnDeliveredOrders();
                }
            });
        }
    }

    public static void setNewVersionPay(int sdkType, final String isNew) {
        final IPay sdk = findPaySDK(sdkType);
        if(sdk != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    sdk.setNewVersion(isNew);
                }
            });
        }
    }

    public static String getBillingConfig(int sdkType) {
        final IPay sdk = findPaySDK(sdkType);
        if(sdk != null) {
            return sdk.getBillingConfig();
        }

        return "";
    }

    public static boolean isFeatureSupported(int sdkType) {
        final IPay sdk = findPaySDK(sdkType);
        if(sdk != null) {
            return sdk.isFeatureSupported();
        }

        return true;
    }

    public static void updatePlayerData(String data) {
        JSONObject json = null;
        try {
            json = new JSONObject(data);
            String uid = json.optString("uid", "unknow");
            thisListener.onSetUserIdToAPM(uid);
//            CrashReport.setUserId(uid);
//            Iterator<String> keys = json.keys();
//            while(keys.hasNext()) {
//                String key = keys.next();
////                CrashReport.setSdkExtraData(activity, key, json.getString(key));
//            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        for(int i = 0; i < adSDKs.size(); ++ i) {
            adSDKs.get(i).uploadPlayerData(json);
        }
    }

//    public static void setBuglyUserSceneTag(int tag) {
//        CrashReport.setUserSceneTag(activity, tag); // 上报后的Crash会显示该标签
//    }

    public static void setBuglyUserData(String key, String value) {
        // 最多可以有50对自定义的key-value（超过则添加失败） key限长50字节，value限长200字节，过长截断；
//        CrashReport.putUserData(activity, key, value);
        thisListener.onAddAPMTag(key, value);
    }

    public static void removeBuglyUserData(String key) {
        thisListener.onRemoveAPMTag(key);
    }

    public static void onPause() {
        if(accountPaySDKs != null) {
            for (int i = 0; i < accountPaySDKs.size(); ++i) {
                accountPaySDKs.get(i).onPause();
            }
        }

        if(paySDKs != null) {
            for (int i = 0; i < paySDKs.size(); ++i) {
                paySDKs.get(i).onPause();
            }
        }

        if(accountSDKs != null) {
            for (int i = 0; i < accountSDKs.size(); ++i) {
                accountSDKs.get(i).onPause();
            }
        }

        if(adSDKs != null) {
            for (int i = 0; i < adSDKs.size(); ++i) {
                adSDKs.get(i).onPause();
            }
        }
        if(moreSDKs != null) {
            for (int i = 0; i < moreSDKs.size(); ++i) {
                moreSDKs.get(i).onPause();
            }
        }

        if(audioSDKs != null) {
            for (int i = 0; i < audioSDKs.size(); ++i) {
                audioSDKs.get(i).onPause();
            }
        }
    }

    public static void onResume() {
        if(accountPaySDKs != null) {
            for (int i = 0; i < accountPaySDKs.size(); ++i) {
                accountPaySDKs.get(i).onResume();
            }
        }

        if(paySDKs != null) {
            for (int i = 0; i < paySDKs.size(); ++i) {
                paySDKs.get(i).onResume();
            }
        }

        if(accountSDKs != null) {
            for (int i = 0; i < accountSDKs.size(); ++i) {
                accountSDKs.get(i).onResume();
            }
        }

        if(adSDKs != null) {
            for (int i = 0; i < adSDKs.size(); ++i) {
                adSDKs.get(i).onResume();
            }
        }

        if(moreSDKs != null) {
            for (int i = 0; i < moreSDKs.size(); ++i) {
                moreSDKs.get(i).onResume();
            }
        }

        if(audioSDKs != null) {
            for (int i = 0; i < audioSDKs.size(); ++i) {
                audioSDKs.get(i).onResume();
            }
        }
    }

    public static void onDestroy() {
        if(accountPaySDKs != null) {
            for (int i = 0; i < accountPaySDKs.size(); ++i) {
                accountPaySDKs.get(i).onDestroy();
            }
        }

        if(paySDKs != null) {
            for (int i = 0; i < paySDKs.size(); ++i) {
                paySDKs.get(i).onDestroy();
            }
        }

        if(accountSDKs != null) {
            for (int i = 0; i < accountSDKs.size(); ++i) {
                accountSDKs.get(i).onDestroy();
            }
        }

        if(adSDKs != null) {
            for (int i = 0; i < adSDKs.size(); ++i) {
                adSDKs.get(i).onDestroy();
            }
        }

        if(moreSDKs != null) {
            for (int i = 0; i < moreSDKs.size(); ++i) {
                moreSDKs.get(i).onDestroy();
            }
        }

        if(audioSDKs != null) {
            for (int i = 0; i < audioSDKs.size(); ++i) {
                audioSDKs.get(i).onDestroy();
            }
        }
    }

    public static void onNewIntent(Intent intent) {
        initPresentationInfo(intent);
        if(accountPaySDKs != null) {
            for (int i = 0; i < accountPaySDKs.size(); ++i) {
                accountPaySDKs.get(i).onNewIntent(intent);
            }
        }

        if(paySDKs != null) {
            for (int i = 0; i < paySDKs.size(); ++i) {
                paySDKs.get(i).onNewIntent(intent);
            }
        }

        if(accountSDKs != null) {
            for (int i = 0; i < accountSDKs.size(); ++i) {
                accountSDKs.get(i).onNewIntent(intent);
            }
        }

        if(adSDKs != null) {
            for (int i = 0; i < adSDKs.size(); ++i) {
                adSDKs.get(i).onNewIntent(intent);
            }
        }

        if(moreSDKs != null) {
            for (int i = 0; i < moreSDKs.size(); ++i) {
                moreSDKs.get(i).onNewIntent(intent);
            }
        }

        if(audioSDKs != null) {
            for (int i = 0; i < audioSDKs.size(); ++i) {
                audioSDKs.get(i).onNewIntent(intent);
            }
        }
    }

    public static void onActivityResult(int requestCode, int resultCode, Intent data) {
        if(accountPaySDKs != null) {
            for (int i = 0; i < accountPaySDKs.size(); ++i) {
                accountPaySDKs.get(i).onActivityResult(requestCode, resultCode, data);
            }
        }

        if(paySDKs != null) {
            for (int i = 0; i < paySDKs.size(); ++i) {
                paySDKs.get(i).onActivityResult(requestCode, resultCode, data);
            }
        }

        if(accountSDKs != null) {
            for (int i = 0; i < accountSDKs.size(); ++i) {
                accountSDKs.get(i).onActivityResult(requestCode, resultCode, data);
            }
        }

        if(adSDKs != null) {
            for (int i = 0; i < adSDKs.size(); ++i) {
                adSDKs.get(i).onActivityResult(requestCode, resultCode, data);
            }
        }

        if(moreSDKs != null) {
            for (int i = 0; i < moreSDKs.size(); ++i) {
                moreSDKs.get(i).onActivityResult(requestCode, resultCode, data);
            }
        }

        if(audioSDKs != null) {
            for (int i = 0; i < audioSDKs.size(); ++i) {
                audioSDKs.get(i).onActivityResult(requestCode, resultCode, data);
            }
        }
    }

    public static void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if(requestCode == REQUEST_PERMISSION_CODE) {
            if(grantResults.length > 0) {
                thisListener.onRequestNotificationPermission(grantResults[0] == PackageManager.PERMISSION_GRANTED, requestNotificationLuaCallback);
            } else {
                thisListener.onRequestNotificationPermission(false, requestNotificationLuaCallback);
            }
        }
    }

    private static void initPresentationInfo(Intent intent) {
        if(intent != null) {
            id = intent.getStringExtra("id");
            localIdentifier = intent.getIntExtra("identifier", -1);
            coinPresentationCount = intent.getIntExtra("coins", 0);
            dollarPresentationCount = intent.getDoubleExtra("dollar", 0.0);
        }
    }

    public static String getPresentInfo() {
        JSONObject json = new JSONObject();
        try {
            json.put("id", id);
            json.put("identifier", localIdentifier);
            json.put("coins", coinPresentationCount);
            json.put("dollar", dollarPresentationCount);
            id = "";
            localIdentifier = -1;
            coinPresentationCount = 0;
            dollarPresentationCount = 0;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String info = json.toString();
        return info;
    }

    public static void processDeeplinkData(Uri uri) {
        if(uri != null) {
            parameters.put("host", uri.getHost());
            parameters.put("path", uri.getPath());
            Log.d(TAG, "processDeeplinkData host=" + parameters.get("host"));
            Log.d(TAG, "processDeeplinkData path=" + parameters.get("path"));
            Set<String> keys = uri.getQueryParameterNames();
            for(String key : keys) {
                String value = uri.getQueryParameter(key);
                Log.d(TAG, "processDeeplinkData key=" + key + ", value=" + value);
                parameters.put(key, value);
            }
        }
    }

    public static String getQueryParameterByKey(String key) {
        if(parameters.containsKey(key)) {
            String value = parameters.get(key);
            parameters.remove(key);
            return value;
        }
        return "";
    }

    public static void copyTextToClipboard(final String text) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ClipboardManager clipboard = (ClipboardManager)activity.getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clip = ClipData.newPlainText("text", text);
                clipboard.setPrimaryClip(clip);
            }
        });
    }

    /**
     * 检查对应的包名的app是否安装
     * @param packageName   要检查的app的包名
     * @param deeplinkScheme    要检查的app的deeplink scheme，这个字段在Android上无用
     * @return
     */
    public static boolean isAppInstalled(String packageName, String deeplinkScheme) {
        try {
            ApplicationInfo info = activity.getPackageManager().getApplicationInfo(packageName, 0);
            Log.d(TAG, "isAppInstalled log 1");
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            Log.d(TAG, "isAppInstalled log 2 ");
            return false;
        }
    }

    /**
     * 打开设备的设置界面
     */
    public static void openAppSettingPage() {
        final Intent intent;
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            intent = new Intent();
            intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            intent.putExtra(Settings.EXTRA_APP_PACKAGE, activity.getPackageName());
            intent.putExtra(Settings.EXTRA_CHANNEL_ID, activity.getApplicationInfo().uid);
        } else {
            intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
            intent.setData(uri);
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                try {
                    activity.startActivity(intent);
                } catch(Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void markApp() {

    }

    // 异步检查通知权限
    public static void checkNotificationPermission(int luaCallback) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                boolean enabled = NotificationManagerCompat.from(activity.getApplicationContext()).areNotificationsEnabled();
                thisListener.onCheckNotificationPermission(enabled, luaCallback);
            }
        }).start();
    }

    // 同步检查通知权限
    public static void checkNotificationPermission2(int luaCallback) {
        checkNotificationLuaCallback = luaCallback;
        boolean enabled = NotificationManagerCompat.from(activity.getApplicationContext()).areNotificationsEnabled();
        thisListener.onCheckNotificationPermission2(enabled, 0);
    }

    public static void requestNotificationPermission(int luaCallback) {
        requestNotificationLuaCallback = luaCallback;
        if(Build.VERSION.SDK_INT >= 33) {
            int granted = ContextCompat.checkSelfPermission(activity, Manifest.permission.POST_NOTIFICATIONS);
            if(granted == PackageManager.PERMISSION_DENIED) {
                requestPermissions(activity, new String[]{Manifest.permission.POST_NOTIFICATIONS}, REQUEST_PERMISSION_CODE);
            } else {
                thisListener.onRequestNotificationPermission(true, requestNotificationLuaCallback);
            }
        } else {
            thisListener.onRequestNotificationPermission(NotificationManagerCompat.from(activity.getApplicationContext()).areNotificationsEnabled(), requestNotificationLuaCallback);
        }
    }

    public static void createShotCut(String websiteUrl, String websiteName, int luaCallback){
        createShortCutLuaCallback = luaCallback;
        if (ShortcutManagerCompat.isRequestPinShortcutSupported(activity.getApplicationContext())) {
            int notification_icon = activity.getApplicationContext().getResources().getIdentifier("voyage_icon", "drawable", activity.getApplicationContext().getPackageName());
            ShortcutInfoCompat shortcut = new ShortcutInfoCompat.Builder(activity.getApplicationContext(), "shortcutId")
                    .setIntent(new Intent(Intent.ACTION_VIEW, Uri.parse(websiteUrl))) // 这里设置你的网址
                    .setIcon(IconCompat.createWithResource(activity.getApplicationContext(), notification_icon)) // 设置图标
                    .setShortLabel(websiteName) // 快捷方式的名称
                    .build();

            PendingIntent shortcutCallbackIntent = PendingIntent.getBroadcast(activity.getApplicationContext(), 0,
                    new Intent(activity.getApplicationContext(), shortcutReceiver.class), PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

            ShortcutManagerCompat.requestPinShortcut(activity.getApplicationContext(), shortcut, shortcutCallbackIntent.getIntentSender());
        } else {
            // 提示用户无法创建快捷方式，并提供跳转到设置页面的选项
            thisListener.onCreateShortCut(false, luaCallback);
        }

    }

    // 设置通知音效
    public static void setNotificationSound(final String channelName, final String soundPath, final boolean playSound) {
        Log.d(TAG, "setNotificationSound channelName="+channelName+", soundPath="+soundPath);
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                createNotificationChannel(activity, channelName, soundPath);

                if(playSound) {
                    Uri uri = null;
                    if(channelName.equals("Default")) {
                        uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
                    } else {
                        if(!TextUtils.isEmpty(soundPath)) {
                            File soundFile = new File(soundPath);
                            if(soundFile.exists()) {
                                uri = Uri.fromFile(soundFile);
                            } else {
                                uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
                            }
                        } else {
                            uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
                        }
                    }
                    Ringtone r = RingtoneManager.getRingtone(activity, uri);
                    if (r == null) {
                        return;
                    }
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                        r.setVolume(1.0f);
                    }
                    r.play();
                }

                SharedPreferences sharedPreferences = activity.getSharedPreferences("NotificationSound", Context.MODE_PRIVATE);
                SharedPreferences.Editor editor = sharedPreferences.edit();
                editor.putString("CHANNEL_NAME", channelName);
                editor.commit();
            }
        });
    }
    public static String getNotificationSoundConfig(Context context) {
        String sound = "Default";
        if(context != null) {
            SharedPreferences sharedPreferences = context.getSharedPreferences("NotificationSound", Context.MODE_PRIVATE);
            sound = sharedPreferences.getString("CHANNEL_NAME", "Default");
        }
        Log.d(TAG, "getNotificationSoundConfig sound="+sound);
        return sound;
    }

    public static void getNotificationSound(final int luaCallback) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                thisListener.onGetNotificationSound(getNotificationSoundConfig(activity), luaCallback);
            }
        });
    }

    public static String getInstalledApps() {
        JSONObject json = new JSONObject();
        try {
            List<PackageInfo> packs = activity.getPackageManager().getInstalledPackages(0);
            for(int i=0;i<packs.size();i++) {
                PackageInfo p = packs.get(i);
                if (p.versionName == null) {
                    continue ;
                }
                String appname = p.applicationInfo.loadLabel(activity.getPackageManager()).toString();
                String pname = p.packageName;
                json.put(appname, pname);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return json.toString();
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public static void getStorageStats(int luaCallback) {
        getStorageStatsLuaCallback = luaCallback;
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            StorageStatsManager storageStatsManager = (StorageStatsManager) activity.getSystemService(Context.STORAGE_STATS_SERVICE);
            StorageManager storageManager = (StorageManager) activity.getSystemService(Context.STORAGE_SERVICE);
            //获取所有应用的StorageVolume列表
            List<StorageVolume> storageVolumes = storageManager.getStorageVolumes();
            for (StorageVolume item : storageVolumes) {
                String uuidStr = item.getUuid();
                UUID uuid;
                if (uuidStr == null) {
                    uuid = StorageManager.UUID_DEFAULT;
                } else {
                    uuid = UUID.fromString(uuidStr);
                }
                int uid = getUid(activity, activity.getPackageName());
                //通过包名获取uid
                StorageStats storageStats = null;
                try {
                    storageStats = storageStatsManager.queryStatsForUid(uuid, uid);
                    thisListener.onGetStorageStats(storageStats.getCacheBytes(), storageStats.getDataBytes(), storageStats.getAppBytes(), 0);
                } catch (IOException e) {
                    e.printStackTrace();
                    thisListener.onGetStorageStats(-1, -1, -1, 0);
                }
            }
        } else {
            thisListener.onGetStorageStats(-1, -1, -1, 0);
        }
    }

    /**
     * 根据应用包名获取对应uid
     */
    public static int getUid(Context context, String pakName) {
        try {
            return context.getPackageManager().getApplicationInfo(pakName, PackageManager.GET_META_DATA).uid;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return -1;
    }

    /**
     * 设置应用的icon，目前发现在Amazon的设备上切换了icon后，游戏启动就会崩溃，应该是设备系统的bug
     * @param id
     * @return
     */
    public static boolean setIconIndex(final int id) {
        if(Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            return false;
        }
        final String[] configs = {"org.cocos2dx.lua.AppActivity", "org.cocos2dx.lua.AppActivityAlias"};
        final String[] splashConfigs = {"org.cocos2dx.lua.SplashActivity", "org.cocos2dx.lua.SplashActivityAlias"};
        if(id >= configs.length) {
            return false;
        }

        changeIconTask = new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "changeIconTask");
                PackageManager pm = activity.getPackageManager();
                for(int i = 0; i < configs.length; ++ i) {
                    pm.setComponentEnabledSetting(new ComponentName(activity, configs[i]),
                            id == i ? PackageManager.COMPONENT_ENABLED_STATE_ENABLED : PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                            PackageManager.DONT_KILL_APP);
//                    pm.setComponentEnabledSetting(new ComponentName(activity, splashConfigs[i]),
//                            id == i ? PackageManager.COMPONENT_ENABLED_STATE_ENABLED : PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
//                            PackageManager.DONT_KILL_APP);
                }
            }
        };
        targetIconId = id;
        return true;
    }

    public static int getIconIndex() {
        if(changeIconTask != null) {
            return targetIconId;
        }
        final String[] configs = {"org.cocos2dx.lua.AppActivity", "org.cocos2dx.lua.AppActivityAlias"};
        PackageManager pm = activity.getPackageManager();
        for(int i = 0; i < configs.length; ++ i) {
            String config = configs[i];
            int ret = pm.getComponentEnabledSetting(new ComponentName(activity, config));
            if(ret == PackageManager.COMPONENT_ENABLED_STATE_DEFAULT || ret == PackageManager.COMPONENT_ENABLED_STATE_ENABLED) {
                return i;
            }
        }
        return 0;
    }

    public static void lifeCycleOnStarted() {
        ++ refCount;
    }

    public static void lifeCycleOnStopped() {
        -- refCount;
        if(refCount == 0 && changeIconTask != null) {
            activity.runOnUiThread(changeIconTask);
            changeIconTask = null;
            targetIconId = 0;
        }
    }

    /**
     * 在Android5.1.1上，emoji通过NewStringUTF转jstring可能会崩溃，所以在这个接口上做一下兼容，不传入String，而是传入byte[]
     * @param id
     * @param _title
     * @param _content
     * @param delay
     */
    public static void notify(int id, byte[] _title, byte[] _content, int delay) {
        try {
            String title = new String(_title, "UTF-8");
            String content = new String(_content, "UTF-8");
            Log.d("NotificationTest:notify 222", "title="+title+" content="+content);
            notify(id, title, content, delay);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    public static void notify(int id, String title, String content, int delay) {
        Intent intent = new Intent(activity.getApplicationContext(), NotificationAlarmReceiver.class);
        intent.putExtra("id", id);
        intent.putExtra("title", title);
        intent.putExtra("content", content);
        Log.d("NotificationTest:notify 333", "id="+id+" title="+title+" content="+content);
        notify(id, intent, delay);
    }

    /**
     * 同样是解决低版本手机上emoji导致的崩溃问题，单独添加一个接口防止崩溃
     * @param id
     * @param _title
     * @param _content
     * @param preview_image_url
     * @param image_url
     * @param delay
     */
    public static void notifyWithImage(int id, byte[] _title, byte[] _content, String preview_image_url, String image_url, int delay) {
        String title = new String(_title, StandardCharsets.UTF_8);
        String content = new String(_content, StandardCharsets.UTF_8);
        Log.d("NotificationTest:notifyWithImage", "title=" + title + " content:"+content);
        notifyWithImage(id, title, content, preview_image_url, image_url, delay);
    }

    public static void notifyWithImage(int id, String title, String content, String preview_image_url, String image_url, int delay) {
        Intent intent = new Intent(activity.getApplicationContext(), NotificationAlarmReceiver.class);
        intent.putExtra("id", id);
        intent.putExtra("title", title);
        intent.putExtra("content", content);
        intent.putExtra("preview_image_url", preview_image_url);
        intent.putExtra("image_url", image_url);
        Log.d("NotificationTest:notifyWithImage", "title=" + title + " content:"+content+" id:"+id+" preview_image_url="+preview_image_url);
        notify(id, intent, delay);
    }

    private static void notify(final int id, final Intent intent, final int delay) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                try {
                    AlarmManager alarmManager = (AlarmManager)activity.getSystemService(Context.ALARM_SERVICE);
                    intent.setClass(activity.getApplicationContext(), NotificationAlarmReceiver.class);
                    PendingIntent alarmIntent;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        Log.d("NotificationTest:notify", "111  id="+id);
                        alarmIntent = PendingIntent.getBroadcast(activity, id, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

                    }else {
                        Log.d("NotificationTest:notify", "222  id="+id);
                        alarmIntent = PendingIntent.getBroadcast(activity, id, intent, PendingIntent.FLAG_UPDATE_CURRENT);
                    }
                    if(Build.VERSION.SDK_INT >= 23) {
//                        alarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME_WAKEUP, SystemClock.elapsedRealtime() + delay * 1000, alarmIntent);
                        Log.d("NotificationTest:notify", "333  id="+id);
                        AlarmManager.AlarmClockInfo alarmClockInfo = new AlarmManager.AlarmClockInfo(delay * 1000L + System.currentTimeMillis(), alarmIntent);
                        alarmManager.setAlarmClock(alarmClockInfo, alarmIntent);
                    } else {
                        Log.d("NotificationTest:notify", "444  id="+id);
                        alarmManager.set(AlarmManager.ELAPSED_REALTIME_WAKEUP, SystemClock.elapsedRealtime() + delay * 1000L, alarmIntent);
                    }
                } catch(Exception e) {
                    Log.e(TAG, "notify failed, error = " + e.getLocalizedMessage());
                }
            }
        });
    }

    private static void createNotificationChannel(Context context, String channelName, String soundPath) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(activity);
            notificationManager.deleteNotificationChannel(channelName);
            NotificationChannel channel = new NotificationChannel(channelName, channelName, NotificationManager.IMPORTANCE_HIGH);
            channel.enableLights(true); //设置开启指示灯，如果设备有的话
            channel.setLightColor(Color.RED); //设置指示灯颜色
            channel.setShowBadge(true); //设置是否显示角标
            channel.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE); //设置是否应在锁定屏幕上显示此频道的通知
            channel.setDescription(channelName); //设置渠道描述
            channel.setVibrationPattern(new long[]{100,200,300,100,200,300}); //设置震动频率
            channel.setBypassDnd(true); //设置是否绕过免打扰模式

            if(!soundPath.isEmpty()) {
                File soundFile = new File(soundPath);
                if (soundFile.exists()) {
                    Uri soundUri = FileProvider.getUriForFile(context, "JackpotWorld", soundFile);
                    channel.setSound(soundUri, new AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                            .build());
                }
            }

            notificationManager.createNotificationChannel(channel);
        }
    }
    /**
     * 发送只有文字内容的推送
     * @param id        推送id
     * @param title		推送标题
     * @param content	推送内容
     * @return
     */
    public static int showCustomNotificationWithOnlyText(int id, Context context, String title, String content, Intent intent)
    {
        if(isInForeground(context)) {
            return 0;
        }
        Log.d(TAG, "UJH_ADD_LOG 1");
        NotificationManager notificationManager = (NotificationManager)context.getSystemService(Context.NOTIFICATION_SERVICE);
        PendingIntent pendingIntent = null;
        if(intent != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

            }else {
                pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, getNotificationSoundConfig(context));
        Log.d(TAG, "UJH_ADD_LOG 2");
        int notification_icon = context.getResources().getIdentifier("notification_icon", "drawable", context.getPackageName());
        builder.setSmallIcon(notification_icon)
                .setAutoCancel(true)
                .setWhen(System.currentTimeMillis())
                .setContentTitle(title)
                .setContentText(content)
                .setPriority(NotificationCompat.PRIORITY_HIGH);
        if(pendingIntent != null) {
            builder.setContentIntent(pendingIntent);
        }
        notificationManager.notify(id, builder.build());
        Log.d(TAG, "UJH_ADD_LOG 3");
        return 0;
    }

    public static int showCustomNotificationWithTwoImages(final int id, final Context context, final String title, final String content, final String preview_image_url, final String image_url, Intent intent) {
        String[] urls = null;
        if(preview_image_url.equals(image_url)) {
            urls = new String[]{preview_image_url};
        } else {
            urls = new String[]{
                    preview_image_url,
                    image_url
            };
        }
        final Intent finalIntent = intent;
        downloadBitmapsAsync(context, urls, new DownloadListener() {
            @Override
            public void onFinished(List<Bitmap> bmps) {
                Bitmap image1 = bmps.size() > 0 ? bmps.get(0) : null;
                Bitmap image2 = bmps.size() > 1 ? bmps.get(1) : null;
                if(preview_image_url.equals(image_url)) {
                    image2 = image1;
                }
                showCustomNotificationWithTwoImages(id, context, title, content, image1, image2, finalIntent);
            }
        });

        return 0;
    }

    public static int showCustomNotificationWithTwoImages(int id, Context context, String title, String content, Bitmap preview_image, Bitmap image, Intent intent) {
        if(isInForeground(context)) {
            return 0;
        }
        if(preview_image != null && image != null) {
            PendingIntent pendingIntent = null;
            if(intent != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

                }else {
                    pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
                }
            }
            int notification_icon_rsc_id = context.getResources().getIdentifier("notification_icon", "drawable", context.getPackageName());
            RemoteViews notification_small = new RemoteViews(context.getPackageName(), R.layout.two_pics_notification);
            notification_small.setImageViewBitmap(R.id.preview, preview_image);
            notification_small.setImageViewResource(R.id.notification_icon, notification_icon_rsc_id);

            RemoteViews notification_big = new RemoteViews(context.getPackageName(), R.layout.two_pics_notification);
            notification_big.setImageViewBitmap(R.id.preview, image);
            notification_big.setImageViewResource(R.id.notification_icon, notification_icon_rsc_id);

            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, getNotificationSoundConfig(context));
            builder.setSmallIcon(notification_icon_rsc_id)
                    .setAutoCancel(true)
                    .setContentIntent(pendingIntent)
                    .setCustomContentView(notification_small)
                    .setCustomBigContentView(notification_big)
                    .setContentIntent(pendingIntent)
                    .setContentTitle(title)
                    .setContentText(content)
                    .setPriority(NotificationCompat.PRIORITY_HIGH);

            NotificationManager notificationManager = (NotificationManager)context.getSystemService(Context.NOTIFICATION_SERVICE);
            notificationManager.notify(id, builder.build());
        } else if(preview_image != null) {
            showCustomNotificationWithOneImage(id, context, title, content, preview_image, intent);
        } else if(image != null) {
            showCustomNotificationWithOneImage(id, context, title, content, image, intent);
        } else {
            showCustomNotificationWithOnlyText(id, context, title, content, intent);
        }
        return 0;
    }

    public static void vibrateWithPattern(String patternStr) {
        for(int i = 0; i < vibrateRunnables.size(); ++ i) {
            vibrateRunnables.get(i).cancel();
        }
        vibrateRunnables.clear();
        try {
            JSONArray pattern = new JSONArray(patternStr);
            if(pattern.length() % 2 == 1) {
                pattern.put(0l);
            }
            long totalTime = 0;
            for(int i = 0; i < pattern.length(); i+=2) {
                VibrateRunnable vibrateRunnable = new VibrateRunnable(pattern.getLong(i));
                handler.postDelayed(vibrateRunnable, totalTime);
                totalTime += pattern.getLong(i);
                totalTime += pattern.getLong(i+1);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static int showCustomNotificationWithOneImage(final int id, final Context context, final String title, final String content, String image_url) {
        Intent intent = null;
        try {
            Class<? extends Activity> clazz = (Class<? extends Activity>) Class.forName("org.cocos2dx.lua.AppActivity");
            intent = new Intent(context, clazz);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        String[] urls = {image_url};
        final Intent finalIntent = intent;
        downloadBitmapsAsync(context, urls, new DownloadListener() {
            @Override
            public void onFinished(List<Bitmap> bmps) {
                Bitmap image = bmps.size() > 0 ? bmps.get(0) : null;
                if(image != null) {
                    showCustomNotificationWithOneImage(id, context, title, content, image, finalIntent);
                } else {
                    showCustomNotificationWithOnlyText(id, context, title, content, finalIntent);
                }
            }
        });
        return 0;
    }

    public static int showCustomNotificationWithOneImage(int id, Context context, String title, String content, Bitmap image, Intent intent) {
        if(isInForeground(context)) {
            return 0;
        }
        int notification_icon_rsc_id = context.getResources().getIdentifier("notification_icon", "drawable", context.getPackageName());
        PendingIntent pendingIntent;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        }else {
            pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, getNotificationSoundConfig(context));
        builder.setSmallIcon(notification_icon_rsc_id)
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .setSound(Uri.parse("android.resource://" + context.getPackageName() + "/raw/n2"))
                .setContentTitle(title)
                .setContentText(content)
                .setPriority(NotificationCompat.PRIORITY_HIGH);
        if(image != null) {
            builder.setLargeIcon(BitmapFactory.decodeResource(context.getResources(), notification_icon_rsc_id))
                    .setStyle(new NotificationCompat.BigPictureStyle()
                            .bigPicture(image)
                            .bigLargeIcon(null));
            NotificationManager notificationManager = (NotificationManager)context.getSystemService(Context.NOTIFICATION_SERVICE);
            notificationManager.notify(id, builder.build());
        } else {
            return showCustomNotificationWithOnlyText(id, context, title, content, intent);
        }
        return 0;
    }

    public static void downloadBitmapsAsync(final Context context, final String[] _urls, final DownloadListener listener) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                List<Bitmap> images = new ArrayList<>();
                for(int i = 0; i < _urls.length; ++ i) {
                    Bitmap image = downloadOrReadBitmap(context, _urls[i]);
                    if(image != null) {
                        images.add(image);
                    }
                }
                if(listener != null) {
                    listener.onFinished(images);
                }
            }
        }).start();
    }

    public static native void testNativeCrash();
    public static native void collectTest(String key, String value);
    public static native void clearCollectedMsg();
    public static native String getCollectedMsg();

    public static void CrashMsgCollectorUnitTest() {
        Thread thread = new Thread(new Runnable(){
            @Override
            public void run() {
                int index = 0;
                while(true) {
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    collectTest("key"+index, "value"+index);
                    ++ index;
                }
            }
        });
        thread.start();

        Thread thread2 = new Thread(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    clearCollectedMsg();
                }
            }
        });
        thread2.start();

        Thread thread3 = new Thread(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    try {
                        Thread.sleep(80);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    Log.d(TAG, getCollectedMsg());
                }
            }
        });
        thread3.start();
    }

    public static void testJavaCrash() {
        JSONObject jsonObject = null;
        Log.d(TAG, jsonObject.toString());
    }

    private static Bitmap downloadOrReadBitmap(Context context, String _url) {
        if(TextUtils.isEmpty(_url)) {
            return null;
        }
        if(_url.startsWith("/")) {
            return BitmapFactory.decodeFile(_url);
        } else if(_url.startsWith("http")) {
            try {
                URL url = new URL(_url);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setDoInput(true);
                connection.connect();
                InputStream input = connection.getInputStream();
                Bitmap bitmap = BitmapFactory.decodeStream(input);
                return bitmap;
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
                return null;
            }
        } else {
            AssetManager assetManager = context.getAssets();
            InputStream istr;
            Bitmap bitmap = null;
            try {
                istr = assetManager.open(_url);
                bitmap = BitmapFactory.decodeStream(istr);
            } catch(IOException e) {
                Log.e(TAG, "failed to read image from " + _url);
            }
            return bitmap;
        }
    }

    public static void onGameLaunchStatus(boolean status) {
        if(thisListener != null) {
            thisListener.onGameLaunchStatus(status);
        }
    }

    public static void onSendShutCutCallback(boolean status){
        if(thisListener != null) {
            thisListener.onCreateShortCut(status, 0);
        }
    }

    public static void share(int sdkType, String data, int luaCallback) {
        final IMore sdk = findMoreSDK(sdkType);
        if(sdk != null) {
            shareLuaCallback = luaCallback;
            JSONObject json = null;
            try {
                json = new JSONObject(data);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sdk.share(json);
        }
    }

    public static boolean isSamsungCloud(int sdkType){

        final IMore sdk = findMoreSDK(sdkType);
        if(sdk != null) {
            return  sdk.isSamsungCloud();
        }

        return false;
    }

    public static void endFirstSessionDelay(int sdkType){
        final IMore sdk = findMoreSDK(sdkType);
        if(sdk != null) {
            sdk.endFirstSessionDelay();
        }
    }

    public static String getSamsungCloudGaid(int sdkType){
        final IMore sdk = findMoreSDK(sdkType);
        if(sdk != null) {
            return sdk.getSamsungCloudGaid();
        }
        return "";
    }

    private interface DownloadListener {
        void onFinished(List<Bitmap> bmps);
    }
}
