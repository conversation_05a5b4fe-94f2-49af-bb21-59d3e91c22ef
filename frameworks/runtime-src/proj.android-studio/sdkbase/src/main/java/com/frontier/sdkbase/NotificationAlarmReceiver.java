package com.frontier.sdkbase;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

// 本地推送，倒计时结束后，执行推送消息显示
public class NotificationAlarmReceiver extends BroadcastReceiver {
    private static final String TAG = "BroadcastReceiver";
    @Override
    public void onReceive(Context context, Intent intent) {
        int id = intent.getIntExtra("id", 0);
        String title = intent.getStringExtra("title");
        String content = intent.getStringExtra("content");
        String preview_image_url = intent.getStringExtra("preview_image_url");
        String image_url = intent.getStringExtra("image_url");
        Log.d(TAG, "onReceive, id = " + id + ", title = " + title + ", content = " + content + ", preview_image_url = " + preview_image_url + ", image_url = " + image_url);
        Intent newIntent = null;
        try {
            Class<? extends Activity> clazz = (Class<? extends Activity>) Class.forName("org.cocos2dx.lua.AppActivity");
            newIntent = new Intent(context, clazz);
            newIntent.putExtra("identifier", id);
            newIntent.putExtra("jwLaunch", 3);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        if(TextUtils.isEmpty(preview_image_url) && TextUtils.isEmpty(image_url)) {
            SDKManager.showCustomNotificationWithOnlyText(id, context, title, content, newIntent);
        } else {
            SDKManager.showCustomNotificationWithTwoImages(id, context, title, content, preview_image_url, image_url, newIntent);
        }
    }
}
